// --------------------------------------
// i2c_scanner
//
// Version 1
//    This program (or code that looks like it)
//    can be found in many places.
//    For example on the Arduino.cc forum.
//    The original author is not know.
// Version 2, Juni 2012, Using Arduino 1.0.1
//     Adapted to be as simple as possible by Arduino.cc user Krodal
// Version 3, Feb 26  2013
//    V3 by <PERSON><PERSON><PERSON><PERSON>
// Version 4, March 3, 2013, Using Arduino 1.0.3
//    by Arduino.cc user <PERSON>rodal.
//    Changes by <PERSON>uarno<PERSON> removed.
//    Scanning addresses changed from 0...127 to 1...119,
//    according to the i2c scanner by <PERSON>
//    https://www.gammon.com.au/forum/?id=10896
// Version 5, March 28, 2013
//    As version 4, but address scans now to 127.
//    A sensor seems to use address 120.
// Version 6, November 27, 2015.
//    Added waiting for the Leonardo serial communication.
//
//
// This sketch tests the standard 7-bit addresses
// Devices with higher bit address might not be seen properly.
//

#include <Wire.h>

// Create a custom I2C2 object for PB11 (SDA), PB10 (SCL)
TwoWire MyI2C2(PB11, PB10); // For STM32duino core

void scanBus(TwoWire &bus, const char* busName) {
  Serial.print("Scanning ");
  Serial.print(busName);
  Serial.println("...");
  for (uint8_t address = 1; address < 127; address++) {
    bus.beginTransmission(address);
    if (bus.endTransmission() == 0) {
      Serial.print("I2C device found at address 0x");
      if (address < 16) Serial.print("0");
      Serial.print(address, HEX);
      Serial.print(" on ");
      Serial.println(busName);
      delay(5);
    }
  }
}

void setup() {
  Serial.begin(115200);
  delay(1000);

  Wire.begin();    // I2C1 (PB9=SDA, PB8=SCL)
  MyI2C2.begin();  // I2C2 (PB11=SDA, PB10=SCL)
  delay(100);
}

void loop() {
  scanBus(Wire, "I2C1");
  scanBus(MyI2C2, "I2C2");
  Serial.println("Scan complete.\n");
  delay(5000);
}
