#include "ds3231.h"
#include "i2c_manager.h"
#include "sdcard.h"
#include <string.h>
#include <stdio.h>

#define DS3231_ADDR 0xD0 // 7-bit address 0x68 shifted left by 1

void DS3231_Init(void) {
    // No special init required for DS3231
}

void DS3231_GetTime(DS3231_TimeTypeDef *time) {
    uint8_t buffer[7];
    I2C_HandleTypeDef* hi2c = I2C_Manager_GetHandle(2); // Use I2C2
    HAL_I2C_Mem_Read(hi2c, DS3231_ADDR, 0x00, 1, buffer, 7, 1000);
    time->seconds = ((buffer[0] >> 4) * 10) + (buffer[0] & 0x0F);
    time->minutes = ((buffer[1] >> 4) * 10) + (buffer[1] & 0x0F);
    time->hours   = ((buffer[2] >> 4) * 10) + (buffer[2] & 0x0F);
    time->day     = buffer[3];
    time->date    = buffer[4];
    time->month   = buffer[5];
    time->year    = buffer[6];
}

void DS3231_SetTime(DS3231_TimeTypeDef *time) {
    uint8_t buffer[7];
    I2C_HandleTypeDef* hi2c = I2C_Manager_GetHandle(2); // Use I2C2
    buffer[0] = ((time->seconds / 10) << 4) | (time->seconds % 10);
    buffer[1] = ((time->minutes / 10) << 4) | (time->minutes % 10);
    buffer[2] = ((time->hours / 10) << 4) | (time->hours % 10);
    buffer[3] = time->day;
    buffer[4] = time->date;
    buffer[5] = time->month;
    buffer[6] = time->year;
    HAL_I2C_Mem_Write(hi2c, DS3231_ADDR, 0x00, 1, buffer, 7, 1000);
}

void Set_RTC_To_Default(void) {
    DS3231_TimeTypeDef time;
    time.seconds = 0;
    time.minutes = 23;
    time.hours = 14;    // 24-hour format for AM PM
    time.day = 2;       // 1=Sun, 2=Mon, 3=Tue, 4=Wed, 5=Fri, 6=Sat, 7=Sun
    time.date = 1;      // 23rd
    time.month = 7;     // June
    time.year = 25;     // 2025
    DS3231_SetTime(&time);
}

float DS3231_GetTemperature(void) {
    I2C_HandleTypeDef* hi2c = I2C_Manager_GetHandle(2); // Use I2C2
    uint8_t temp_reg[2] = {0};
    if (HAL_I2C_Mem_Read(hi2c, DS3231_ADDR, 0x11, 1, temp_reg, 2, 1000) != HAL_OK) {
        SD_Debug_Print("[DS3231] Temp read FAIL\r\n");
        return -127.0f;
    }
    int8_t temp_msb = temp_reg[0];
    uint8_t temp_lsb = temp_reg[1];
    float temp = temp_msb + ((temp_lsb >> 6) * 0.25f);
    char debug[64];
    snprintf(debug, sizeof(debug), "[DS3231] MSB:0x%02X LSB:0x%02X T:%.2f\r\n", temp_msb, temp_lsb, temp);
    SD_Debug_Print(debug);
    return temp;
}
