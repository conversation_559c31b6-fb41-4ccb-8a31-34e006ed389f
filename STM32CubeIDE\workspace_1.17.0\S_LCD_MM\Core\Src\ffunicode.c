/* Minimal ffunicode.c for ASCII-only FatFs builds */
#include "ff.h"

WCHAR ff_oem2uni (WCHAR oem, WORD cp) {
    // For ASCII, just return the input
    return (oem < 0x80) ? oem : '?';
}

WCHAR ff_uni2oem (DWORD uni, WORD cp) {
    // For ASCII, just return the input
    return (uni < 0x80) ? (WCHAR)uni : '?';
}

DWORD ff_wtoupper (DWORD uni) {
    // For ASCII, convert a-z to A-Z
    if (uni >= 'a' && uni <= 'z') return uni - 0x20;
    return uni;
} 