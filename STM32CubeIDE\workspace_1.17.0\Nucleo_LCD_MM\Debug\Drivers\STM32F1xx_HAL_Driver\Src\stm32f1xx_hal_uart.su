../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:354:19:HAL_UART_Init	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:432:19:HAL_HalfDuplex_Init	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:508:19:HAL_LIN_Init	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:592:19:HAL_MultiProcessor_Init	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:672:19:HAL_UART_DeInit	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:718:13:HAL_UART_MspInit	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:733:13:HAL_UART_MspDeInit	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1138:19:HAL_UART_Transmit	48	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1224:19:HAL_UART_Receive	48	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1311:19:HAL_UART_Transmit_IT	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1350:19:HAL_UART_Receive_IT	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1382:19:HAL_UART_Transmit_DMA	56	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1444:19:HAL_UART_Receive_DMA	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1471:19:HAL_UART_DMAPause	120	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1502:19:HAL_UART_DMAResume	120	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1536:19:HAL_UART_DMAStop	72	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1591:19:HAL_UARTEx_ReceiveToIdle	40	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1716:19:HAL_UARTEx_ReceiveToIdle_IT	56	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1776:19:HAL_UARTEx_ReceiveToIdle_DMA	56	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1841:29:HAL_UARTEx_GetRxEventType	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1859:19:HAL_UART_Abort	136	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1948:19:HAL_UART_AbortTransmit	64	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:1999:19:HAL_UART_AbortReceive	112	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2060:19:HAL_UART_Abort_IT	144	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2195:19:HAL_UART_AbortTransmit_IT	64	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2272:19:HAL_UART_AbortReceive_IT	112	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2350:6:HAL_UART_IRQHandler	240	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2592:13:HAL_UART_TxCpltCallback	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2607:13:HAL_UART_TxHalfCpltCallback	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2622:13:HAL_UART_RxCpltCallback	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2637:13:HAL_UART_RxHalfCpltCallback	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2652:13:HAL_UART_ErrorCallback	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2666:13:HAL_UART_AbortCpltCallback	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2681:13:HAL_UART_AbortTransmitCpltCallback	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2696:13:HAL_UART_AbortReceiveCpltCallback	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2713:13:HAL_UARTEx_RxEventCallback	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2753:19:HAL_LIN_SendBreak	40	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2780:19:HAL_MultiProcessor_EnterMuteMode	40	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2808:19:HAL_MultiProcessor_ExitMuteMode	40	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2836:19:HAL_HalfDuplex_EnableTransmitter	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2871:19:HAL_HalfDuplex_EnableReceiver	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2928:23:HAL_UART_GetState	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2943:10:HAL_UART_GetError	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:2988:13:UART_DMATransmitCplt	72	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3023:13:UART_DMATxHalfCplt	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3042:13:UART_DMAReceiveCplt	120	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3104:13:UART_DMARxHalfCplt	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3143:13:UART_DMAError	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3185:26:UART_WaitOnFlagUntilTimeout	32	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3236:19:UART_Start_Receive_IT	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3271:19:UART_Start_Receive_DMA	104	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3321:13:UART_EndTxTransfer	40	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3335:13:UART_EndRxTransfer	88	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3359:13:UART_DMAAbortOnError	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3383:13:UART_DMATxAbortCallback	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3429:13:UART_DMARxAbortCallback	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3475:13:UART_DMATxOnlyAbortCallback	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3503:13:UART_DMARxOnlyAbortCallback	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3529:26:UART_Transmit_IT	24	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3569:26:UART_EndTransmit_IT	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3594:26:UART_Receive_IT	56	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c:3695:13:UART_SetConfig	24	static
