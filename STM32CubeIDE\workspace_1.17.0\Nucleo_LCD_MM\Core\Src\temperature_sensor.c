#include "temperature_sensor.h"
#include "stm32f1xx_hal.h"
#include "main.h"
#include <stdint.h>
#include <stdio.h>

extern void SD_Debug_Print(const char* msg);

// Define 4 sensors on different pins (update as per your wiring)
typedef struct {
    GPIO_TypeDef* port;
    uint16_t pin;
} TempSensorPinDef;

static const TempSensorPinDef temp_sensors[4] = {
    {GPIOA, GPIO_PIN_9},  // Sensor 1
    {GPIOA, GPIO_PIN_10}, // Sensor 2
    {GPIOA, GPIO_PIN_11}, // Sensor 3
    {GPIOA, GPIO_PIN_12}  // Sensor 4
};

// Remove the DWT_Delay_us macro override and use delay_us(x) from delay_dwt.h

static void DS18B20_Pin_Output(uint8_t idx) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = temp_sensors[idx].pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(temp_sensors[idx].port, &GPIO_InitStruct);
}

static void DS18B20_Pin_Input(uint8_t idx) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = temp_sensors[idx].pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(temp_sensors[idx].port, &GPIO_InitStruct);
}

static void DS18B20_WriteBit(uint8_t idx, uint8_t bit) {
    DS18B20_Pin_Output(idx);
    HAL_GPIO_WritePin(temp_sensors[idx].port, temp_sensors[idx].pin, GPIO_PIN_RESET);
    if (bit) {
        delay_us(1);
        DS18B20_Pin_Input(idx);
        delay_us(60);
    } else {
        delay_us(60);
        DS18B20_Pin_Input(idx);
    }
}

static uint8_t DS18B20_ReadBit(uint8_t idx) {
    uint8_t bit = 0;
    DS18B20_Pin_Output(idx);
    HAL_GPIO_WritePin(temp_sensors[idx].port, temp_sensors[idx].pin, GPIO_PIN_RESET);
    delay_us(1);
    DS18B20_Pin_Input(idx);
    delay_us(7);
    bit = HAL_GPIO_ReadPin(temp_sensors[idx].port, temp_sensors[idx].pin);
    delay_us(30);
    // optional debug if temp does not show any value
    // char msg[32];
    // snprintf(msg, sizeof(msg), "DS18B20 idx %d: bit=%d\r\n", idx, bit);
    // SD_Debug_Print(msg);
    
    return bit;
}

static void DS18B20_WriteByte(uint8_t idx, uint8_t byte) {
    for (int i = 0; i < 8; i++) {
        DS18B20_WriteBit(idx, byte & 0x01);
        byte >>= 1;
    }
}

static uint8_t DS18B20_ReadByte(uint8_t idx) {
    uint8_t value = 0;
    for (int i = 0; i < 8; i++) {
        value |= (DS18B20_ReadBit(idx) << i);
    }
    return value;
}

static uint8_t DS18B20_Reset(uint8_t idx) {
    uint8_t response = 0;
    DS18B20_Pin_Output(idx);
    HAL_GPIO_WritePin(temp_sensors[idx].port, temp_sensors[idx].pin, GPIO_PIN_RESET);
    delay_us(480);
    DS18B20_Pin_Input(idx);
    delay_us(80);
    response = HAL_GPIO_ReadPin(temp_sensors[idx].port, temp_sensors[idx].pin);
    delay_us(400);
    return response == GPIO_PIN_RESET ? 1 : 0;
}

void TemperatureSensor_Init_All(void) {
    for (int i = 0; i < 4; i++) {
        DS18B20_Pin_Input(i);
    }
}

float TemperatureSensor_GetTemperature(uint8_t idx) {
    if (idx > 3) return -127.0f;
    uint8_t temp_lsb, temp_msb;
    int16_t temp_raw;
    float temp_c = -127.0f;
    // Try up to 2 times for robustness
    for (int attempt = 0; attempt < 2; ++attempt) {
        if (!DS18B20_Reset(idx)) continue;
        DS18B20_WriteByte(idx, 0xCC); // Skip ROM
        DS18B20_WriteByte(idx, 0x44); // Convert T
        HAL_Delay(750); // Wait for conversion (max 750ms)
        if (!DS18B20_Reset(idx)) continue;
        DS18B20_WriteByte(idx, 0xCC); // Skip ROM
        DS18B20_WriteByte(idx, 0xBE); // Read Scratchpad
        temp_lsb = DS18B20_ReadByte(idx);
        temp_msb = DS18B20_ReadByte(idx);
        temp_raw = (int16_t)((temp_msb << 8) | temp_lsb);
        temp_c = (float)temp_raw / 16.0f;
        // Debug print
        char debug[64];
        snprintf(debug, sizeof(debug), "[DS18B20] LSB:0x%02X MSB:0x%02X RAW:%d T:%.2f\r\n", temp_lsb, temp_msb, temp_raw, temp_c);
        SD_Debug_Print(debug);
        // Accept only plausible values
        if (temp_c > -55.0f && temp_c < 125.0f) return temp_c;
        HAL_Delay(100); // Wait before retry
    }
    return temp_c;
} 
