#ifndef __LORA_H
#define __LORA_H

#include "stm32f1xx_hal.h"
#include <stdint.h>
#include <stdbool.h>

// Pin definitions
#define LORA_RESET_Pin GPIO_PIN_0
#define LORA_RESET_GPIO_Port GPIOB
#define LORA_NSS_Pin GPIO_PIN_1
#define LORA_NSS_GPIO_Port GPIOA
#define LORA_DIO0_Pin GPIO_PIN_4
#define LORA_DIO0_GPIO_Port GPIOA

// SX1276 register addresses (partial, add more as needed)
#define REG_FIFO                    0x00
#define REG_OP_MODE                 0x01
#define REG_FRF_MSB                 0x06
#define REG_FRF_MID                 0x07
#define REG_FRF_LSB                 0x08
#define REG_PA_CONFIG               0x09
#define REG_LNA                     0x0C
#define REG_FIFO_ADDR_PTR           0x0D
#define REG_FIFO_TX_BASE_ADDR       0x0E
#define REG_FIFO_RX_BASE_ADDR       0x0F
#define REG_FIFO_RX_CURRENT_ADDR    0x10
#define REG_IRQ_FLAGS               0x12
#define REG_RX_NB_BYTES             0x13
#define REG_PKT_RSSI_VALUE          0x1A
#define REG_PKT_SNR_VALUE           0x1B
#define REG_MODEM_CONFIG_1          0x1D
#define REG_MODEM_CONFIG_2          0x1E
#define REG_PREAMBLE_MSB            0x20
#define REG_PREAMBLE_LSB            0x21
#define REG_PAYLOAD_LENGTH          0x22
#define REG_MODEM_CONFIG_3          0x26
#define REG_FREQ_ERROR_MSB          0x28
#define REG_FREQ_ERROR_MID          0x29
#define REG_FREQ_ERROR_LSB          0x2A
#define REG_RSSI_WIDEBAND           0x2C
#define REG_DETECTION_OPTIMIZE      0x31
#define REG_DETECTION_THRESHOLD     0x37
#define REG_SYNC_WORD               0x39
#define REG_DIO_MAPPING_1           0x40
#define REG_VERSION                 0x42
#define REG_PA_DAC                  0x4D

// LoRa modes
#define LORA_MODE_SLEEP             0x00
#define LORA_MODE_STDBY             0x01
#define LORA_MODE_TX                0x03
#define LORA_MODE_RXCONTINUOUS      0x05
#define LORA_MODE_RXSINGLE          0x06

// IRQ Flags
#define IRQ_TX_DONE_MASK            0x08
#define IRQ_RX_DONE_MASK            0x40
#define IRQ_PAYLOAD_CRC_ERROR_MASK  0x20

// LoRa status
typedef enum {
    LORA_OK = 0,
    LORA_TIMEOUT,
    LORA_ERROR
} LORA_StatusTypeDef;

void LORA_Init(void);
LORA_StatusTypeDef LORA_Send(uint8_t* data, uint8_t len, uint32_t timeout);
LORA_StatusTypeDef LORA_Receive(uint8_t* buffer, uint8_t maxlen, uint32_t timeout, uint8_t* out_len);
void LORA_SetFrequency(uint32_t freq_hz);
void LORA_SetTxPower(int8_t power_dbm);
void LORA_SetSpreadingFactor(uint8_t sf);
void LORA_SetBandwidth(uint8_t bw);
void LORA_SetCodingRate(uint8_t cr);
void LORA_SetSyncWord(uint8_t sw);
void LORA_SetMode(uint8_t mode);
void LORA_DIO0_IRQHandler(void);
bool LORA_IsChannelFree(void);

#endif // __LORA_H 