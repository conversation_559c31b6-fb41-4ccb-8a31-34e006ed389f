#include "main.h"
#include "led.h"

// LED states
static uint8_t LED1_State = LED_OFF;
static uint8_t LED2_State = LED_OFF;

void LED_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();
    GPIO_InitStruct.Pin = LED1_PIN | LED2_PIN | LED3_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    GPIO_InitStruct.Pin = SWITCH1_PIN | SWITCH2_PIN | SWITCH3_PIN | SWITCH4_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(SWITCH_A_PORT, &GPIO_InitStruct);
    GPIO_InitStruct.Pin = SWITCH5_PIN | SWITCH6_PIN | SWITCH7_PIN | SWITCH8_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(SWITCH_B_PORT, &GPIO_InitStruct);
    HAL_GPIO_WritePin(LED1_GPIO_PORT, LED1_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LED2_GPIO_PORT, LED2_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LED3_GPIO_PORT, LED3_PIN, GPIO_PIN_RESET);
}

void LED_Process(void)
{
    HAL_GPIO_WritePin(LED1_GPIO_PORT, LED1_PIN, LED1_State ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LED2_GPIO_PORT, LED2_PIN, LED2_State ? GPIO_PIN_SET : GPIO_PIN_RESET);
}

void LED3_Blink(void)
{
    static uint32_t previousTime = 0;
    static uint8_t ledState = 0;
    uint32_t currentTime = HAL_GetTick();
    if (currentTime - previousTime >= 1000) {
        ledState = !ledState;
        HAL_GPIO_WritePin(LED3_GPIO_PORT, LED3_PIN, ledState ? GPIO_PIN_SET : GPIO_PIN_RESET);
        previousTime = currentTime;
    }
}

void LED_SwitchControl(void)
{
    uint8_t switchA_state = 0;
    uint8_t switchB_state = 0;
    if(HAL_GPIO_ReadPin(SWITCH_A_PORT, SWITCH1_PIN) == GPIO_PIN_RESET) switchA_state |= 0x01;
    if(HAL_GPIO_ReadPin(SWITCH_A_PORT, SWITCH2_PIN) == GPIO_PIN_RESET) switchA_state |= 0x02;
    if(HAL_GPIO_ReadPin(SWITCH_A_PORT, SWITCH3_PIN) == GPIO_PIN_RESET) switchA_state |= 0x04;
    if(HAL_GPIO_ReadPin(SWITCH_A_PORT, SWITCH4_PIN) == GPIO_PIN_RESET) switchA_state |= 0x08;
    if(HAL_GPIO_ReadPin(SWITCH_B_PORT, SWITCH5_PIN) == GPIO_PIN_RESET) switchB_state |= 0x01;
    if(HAL_GPIO_ReadPin(SWITCH_B_PORT, SWITCH6_PIN) == GPIO_PIN_RESET) switchB_state |= 0x02;
    if(HAL_GPIO_ReadPin(SWITCH_B_PORT, SWITCH7_PIN) == GPIO_PIN_RESET) switchB_state |= 0x04;
    if(HAL_GPIO_ReadPin(SWITCH_B_PORT, SWITCH8_PIN) == GPIO_PIN_RESET) switchB_state |= 0x08;
    LED1_State = (switchA_state > 0) ? LED_ON : LED_OFF;
    LED2_State = (switchB_state > 0) ? LED_ON : LED_OFF;
}

void LED_Init_PA15(void) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_15;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
}

void LED_On_PA15(void) {
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_15, GPIO_PIN_SET);
}

void LED_Off_PA15(void) {
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_15, GPIO_PIN_RESET);
}

void LED_Toggle_PA15(void) {
    HAL_GPIO_TogglePin(GPIOA, GPIO_PIN_15);
} 