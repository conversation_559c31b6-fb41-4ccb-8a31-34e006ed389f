/*-----------------------------------------------------------------------*/
/* RAM disk control module for Win32              (C)ChaN, 2014          */
/*-----------------------------------------------------------------------*/

#include "diskio.h"
#include "ff.h"
#include "sdcard.h"
#include <string.h>

// Only one drive (SD card) supported
#define SD_DRIVE 0

/*--------------------------------------------------------------------------

   Module Private Functions

---------------------------------------------------------------------------*/


/*--------------------------------------------------------------------------

   Public Functions

---------------------------------------------------------------------------*/


/*-----------------------------------------------------------------------*/
/* Initialize Disk Drive                                                 */
/*-----------------------------------------------------------------------*/

DSTATUS disk_initialize(BYTE pdrv) {
	if (pdrv != SD_DRIVE) return STA_NOINIT;
	// Assume SD_Card_Init is called elsewhere (main init)
	return 0;
}



/*-----------------------------------------------------------------------*/
/* Get Disk Status                                                       */
/*-----------------------------------------------------------------------*/

DSTATUS disk_status(BYTE pdrv) {
	if (pdrv != SD_DRIVE) return STA_NOINIT;
	return 0;
}



/*-----------------------------------------------------------------------*/
/* Read Sector(s)                                                        */
/*-----------------------------------------------------------------------*/

DRESULT disk_read(BYTE pdrv, BYTE *buff, DWORD sector, UINT count) {
	if (pdrv != SD_DRIVE) return RES_PARERR;
	for (UINT i = 0; i < count; i++) {
		if (!SD_Card_ReadBlock(sector + i, buff + i * 512))
			return RES_ERROR;
	}
	return RES_OK;
}



/*-----------------------------------------------------------------------*/
/* Write Sector(s)                                                       */
/*-----------------------------------------------------------------------*/

DRESULT disk_write(BYTE pdrv, const BYTE *buff, DWORD sector, UINT count) {
#if _USE_WRITE
	if (pdrv != SD_DRIVE) return RES_PARERR;
	for (UINT i = 0; i < count; i++) {
		if (!SD_Card_WriteBlock(sector + i, buff + i * 512))
			return RES_ERROR;
	}
	return RES_OK;
#else
	return RES_WRPRT;
#endif
}



/*-----------------------------------------------------------------------*/
/* Miscellaneous Functions                                               */
/*-----------------------------------------------------------------------*/

DRESULT disk_ioctl(BYTE pdrv, BYTE cmd, void *buff) {
	if (pdrv != SD_DRIVE) return RES_PARERR;
	switch (cmd) {
		case CTRL_SYNC:
			return RES_OK;
		case GET_SECTOR_COUNT:
			*(DWORD*)buff = 32768; // Example: 16MB card (32768*512)
			return RES_OK;
		case GET_SECTOR_SIZE:
			*(WORD*)buff = 512;
			return RES_OK;
		case GET_BLOCK_SIZE:
			*(DWORD*)buff = 1;
			return RES_OK;
		default:
			return RES_PARERR;
	}
}

DWORD get_fattime(void) {
	return 0;
}


