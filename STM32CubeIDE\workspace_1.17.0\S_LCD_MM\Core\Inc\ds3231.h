#ifndef DS3231_H
#define DS3231_H

#include "stm32f1xx_hal.h"
#include <stdint.h>

#define DS3231_HAS_TEMP

typedef struct {
    uint8_t seconds;
    uint8_t minutes;
    uint8_t hours;
    uint8_t day;
    uint8_t date;
    uint8_t month;
    uint8_t year;
} DS3231_TimeTypeDef;

void DS3231_Init(void);
void DS3231_GetTime(DS3231_TimeTypeDef *time);
void DS3231_SetTime(DS3231_TimeTypeDef *time);
void Set_RTC_To_Default(void);

#endif // DS3231_H 