/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f1xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define B1_Pin GPIO_PIN_13
#define B1_GPIO_Port GPIOC
#define B1_EXTI_IRQn EXTI15_10_IRQn
#define LORA_NSS_Pin GPIO_PIN_1
#define LORA_NSS_GPIO_Port GPIOA
#define USART_TX_Pin GPIO_PIN_2
#define USART_TX_GPIO_Port GPIOA
#define USART_RX_Pin GPIO_PIN_3
#define USART_RX_GPIO_Port GPIOA
#define LORA_DIO_Pin GPIO_PIN_4
#define LORA_DIO_GPIO_Port GPIOA
#define LORA_RESET_Pin GPIO_PIN_0
#define LORA_RESET_GPIO_Port GPIOB
#define SCL_RTC_Pin GPIO_PIN_10
#define SCL_RTC_GPIO_Port GPIOB
#define SDA_RTC_Pin GPIO_PIN_11
#define SDA_RTC_GPIO_Port GPIOB
#define CS_SDCARD_Pin GPIO_PIN_12
#define CS_SDCARD_GPIO_Port GPIOB
#define RELAY_1_Pin GPIO_PIN_6
#define RELAY_1_GPIO_Port GPIOC
#define RELAY_2_Pin GPIO_PIN_7
#define RELAY_2_GPIO_Port GPIOC
#define RELAY_3_Pin GPIO_PIN_8
#define RELAY_3_GPIO_Port GPIOC
#define RELAY_4_Pin GPIO_PIN_9
#define RELAY_4_GPIO_Port GPIOC
#define CS_LORA_Pin GPIO_PIN_8
#define CS_LORA_GPIO_Port GPIOA
#define TEMPERATURE_SENSOR_1_Pin GPIO_PIN_9
#define TEMPERATURE_SENSOR_1_GPIO_Port GPIOA
#define TEMPERATURE_SENSOR_2_Pin GPIO_PIN_10
#define TEMPERATURE_SENSOR_2_GPIO_Port GPIOA
#define TEMPERATURE_SENSOR_3_Pin GPIO_PIN_11
#define TEMPERATURE_SENSOR_3_GPIO_Port GPIOA
#define TEMPERATURE_SENSOR_4_Pin GPIO_PIN_12
#define TEMPERATURE_SENSOR_4_GPIO_Port GPIOA
#define TMS_Pin GPIO_PIN_13
#define TMS_GPIO_Port GPIOA
#define TCK_Pin GPIO_PIN_14
#define TCK_GPIO_Port GPIOA
#define LD2_Pin GPIO_PIN_15
#define LD2_GPIO_Port GPIOA
#define SWO_Pin GPIO_PIN_3
#define SWO_GPIO_Port GPIOB

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
