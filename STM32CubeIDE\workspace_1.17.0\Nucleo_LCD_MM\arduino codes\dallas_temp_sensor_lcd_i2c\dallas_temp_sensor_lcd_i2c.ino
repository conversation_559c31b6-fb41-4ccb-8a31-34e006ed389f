#include <Wire.h> // Required for I2C communication (for LCD)
#include <LiquidCrystal_I2C.h> // For I2C LCD
#include <OneWire.h> // For 1-Wire protocol
#include <DallasTemperature.h> // For DS18B20 specific functions

// --- DS18B20 Configuration ---
// Data wire is plugged into digital pin 2 on the Nucleo board (corresponds to PA10)
// Data wire is plugged into PA9 on the Nucleo (which is typically D8 on Arduino headers)
#define ONE_WIRE_BUS PA9
// Setup a oneWire instance to communicate with any OneWire devices (not just DS18B20s)
OneWire oneWire(ONE_WIRE_BUS);

// Pass our oneWire reference to Dallas Temperature.
DallasTemperature sensors(&oneWire);

// Optional: Store the device address if you have only one, or want to target a specific one
// If you have multiple sensors, use the OneWireSearch example to find their addresses.
DeviceAddress tempSensorAddress; // Array to hold the 64-bit address

// --- I2C LCD Configuration ---
// Set the LCD I2C address, number of columns, and number of rows
// COMMON ADDRESSES: 0x27 or 0x3F. YOU MUST VERIFY YOURS!
// Use an I2C scanner sketch to find the correct address if 0x27 doesn't work.
LiquidCrystal_I2C lcd(0x27, 16, 2); // Example: 0x27 address, 16 columns, 2 rows

void setup(void)
{
  // --- Initialize Serial Communication ---
  Serial.begin(115200); // Set baud rate for serial monitor
  Serial.println("Nucleo Temperature Monitor");
  Serial.println("--------------------------");

  // --- Initialize I2C LCD ---
  // lcd.init() returns a boolean in some versions, but not all.
  // We'll call it and assume it works for basic setup.
  lcd.init(); 
  // If lcd.init() fails silently (doesn't return true/false)
  // or your library version doesn't have isInitialized(),
  // just proceed with print.
  // If the LCD remains blank after adjusting contrast,
  // the I2C address (0x27) is the next thing to check!

  lcd.backlight(); // Turn on LCD backlight
  lcd.clear(); // Clear any garbage on the display
  lcd.setCursor(0, 0); // Set cursor to column 0, row 0 (top-left)
  lcd.print("Temp Monitor");


  // --- Initialize DS18B20 Sensor ---
  sensors.begin(); // Start up the DallasTemperature library

  // Optional: Verify sensor presence and get its address
  if (!sensors.getAddress(tempSensorAddress, 0)) { // Get address of first sensor found
    Serial.println("DS18B20 not found or error getting address!");
    Serial.println("Check wiring (especially pull-up), power, and correct pin for ONE_WIRE_BUS.");
  } else {
    Serial.print("Found DS18B20 at address: ");
    // Print the address in HEX
    for (uint8_t i = 0; i < 8; i++) {
      if (tempSensorAddress[i] < 0x10) Serial.print("0");
      Serial.print(tempSensorAddress[i], HEX);
    }
    Serial.println();

    // Set the resolution for this specific sensor to 12-bit (highest accuracy)
    // This is important for precise readings and might resolve the -0.06 issue.
    sensors.setResolution(tempSensorAddress, 12, true); // 12-bit resolution, wait for it to be set
    Serial.print("Sensor resolution set to: ");
    Serial.print(sensors.getResolution(tempSensorAddress));
    Serial.println(" bits.");
  }

}

void loop(void)
{
  // Request temperature conversion from all devices on the bus
  // This takes time (up to 750ms for 12-bit resolution)
  Serial.print("Requesting temperatures...");
  sensors.requestTemperatures();
  Serial.println("DONE");

  // Get temperature from the first sensor found (index 0)
  float tempC = sensors.getTempCByIndex(0);

  // --- Display Temperature on Serial Monitor ---
  if (tempC != DEVICE_DISCONNECTED_C) {
    Serial.print("Temperature (C): ");
    Serial.println(tempC, 2); // Print with 2 decimal places
    Serial.print("Temperature (F): ");
    Serial.println(DallasTemperature::toFahrenheit(tempC), 2); // Print in Fahrenheit
  } else {
    Serial.println("Error: Could not read temperature data (Sensor Disconnected)");
  }

  // --- Display Temperature on I2C LCD ---
  // Since we removed isInitialized(), we assume init() was successful.
  // If LCD doesn't show anything, check contrast & I2C address!
  lcd.setCursor(0, 1); // Set cursor to column 0, row 1 (bottom-left)
  if (tempC != DEVICE_DISCONNECTED_C) {
    lcd.print("Temp: ");
    lcd.print(tempC, 1); // Print with 1 decimal place on LCD to save space
    lcd.print((char)223); // Degree symbol
    lcd.print("C   "); // Add spaces to clear any old characters
  } else {
    lcd.print("Sensor Error!   "); // Message if sensor fails
  }

  delay(2000); // Wait for 2 seconds before next reading
}

// REMOVED: bool LiquidCrystal_I2C::isInitialized() as it's not supported by your library version.