#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.I2C1_TX.0.Direction=DMA_MEMORY_TO_PERIPH
Dma.I2C1_TX.0.Instance=DMA1_Channel6
Dma.I2C1_TX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.I2C1_TX.0.MemInc=DMA_MINC_ENABLE
Dma.I2C1_TX.0.Mode=DMA_NORMAL
Dma.I2C1_TX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.I2C1_TX.0.PeriphInc=DMA_PINC_DISABLE
Dma.I2C1_TX.0.Priority=DMA_PRIORITY_LOW
Dma.I2C1_TX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.Request0=I2C1_TX
Dma.Request1=SPI1_RX
Dma.Request2=SPI1_TX
Dma.RequestsNb=3
Dma.SPI1_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.SPI1_RX.1.Instance=DMA1_Channel2
Dma.SPI1_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.SPI1_RX.1.MemInc=DMA_MINC_ENABLE
Dma.SPI1_RX.1.Mode=DMA_NORMAL
Dma.SPI1_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.SPI1_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.SPI1_RX.1.Priority=DMA_PRIORITY_LOW
Dma.SPI1_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.SPI1_TX.2.Direction=DMA_MEMORY_TO_PERIPH
Dma.SPI1_TX.2.Instance=DMA1_Channel3
Dma.SPI1_TX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.SPI1_TX.2.MemInc=DMA_MINC_ENABLE
Dma.SPI1_TX.2.Mode=DMA_NORMAL
Dma.SPI1_TX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.SPI1_TX.2.PeriphInc=DMA_PINC_DISABLE
Dma.SPI1_TX.2.Priority=DMA_PRIORITY_LOW
Dma.SPI1_TX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
File.Version=6
KeepUserPlacement=false
Mcu.CPN=STM32F103C8T6
Mcu.Family=STM32F1
Mcu.IP0=DMA
Mcu.IP1=I2C1
Mcu.IP2=NVIC
Mcu.IP3=RCC
Mcu.IP4=RTC
Mcu.IP5=SPI1
Mcu.IP6=SYS
Mcu.IP7=TIM2
Mcu.IP8=USART2
Mcu.IPNb=9
Mcu.Name=STM32F103C(8-B)Tx
Mcu.Package=LQFP48
Mcu.Pin0=PC14-OSC32_IN
Mcu.Pin1=PC15-OSC32_OUT
Mcu.Pin10=PB12
Mcu.Pin11=PB13
Mcu.Pin12=PB6
Mcu.Pin13=PB7
Mcu.Pin14=VP_RTC_VS_RTC_Activate
Mcu.Pin15=VP_SYS_VS_ND
Mcu.Pin16=VP_SYS_VS_Systick
Mcu.Pin17=VP_TIM2_VS_ClockSourceINT
Mcu.Pin2=PD0-OSC_IN
Mcu.Pin3=PD1-OSC_OUT
Mcu.Pin4=PA2
Mcu.Pin5=PA3
Mcu.Pin6=PA4
Mcu.Pin7=PA5
Mcu.Pin8=PA6
Mcu.Pin9=PA7
Mcu.PinsNb=18
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103C8Tx
MxCube.Version=6.13.0
MxDb.Version=DB.6.0.130
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Channel2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Channel3_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Channel6_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.I2C1_ER_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.I2C1_EV_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.RTC_Alarm_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.RTC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.SPI1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA2.Mode=Synchronous
PA2.Signal=USART2_TX
PA3.Mode=Synchronous
PA3.Signal=USART2_RX
PA4.Mode=Synchronous
PA4.Signal=USART2_CK
PA5.Mode=Full_Duplex_Master
PA5.Signal=SPI1_SCK
PA6.Mode=Full_Duplex_Master
PA6.Signal=SPI1_MISO
PA7.Mode=Full_Duplex_Master
PA7.Signal=SPI1_MOSI
PB12.GPIOParameters=GPIO_Label
PB12.GPIO_Label=CS_SDCARD
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.GPIOParameters=GPIO_Label
PB13.GPIO_Label=CS_LORA
PB13.Locked=true
PB13.Signal=GPIO_Output
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PD0-OSC_IN.Mode=HSE-External-Oscillator
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Mode=HSE-External-Oscillator
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103C8Tx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=S_LCD_MM.ioc
ProjectManager.ProjectName=S_LCD_MM
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_I2C1_Init-I2C1-false-HAL-true,5-MX_TIM2_Init-TIM2-false-HAL-true,6-MX_RTC_Init-RTC-false-HAL-true,7-MX_SPI1_Init-SPI1-false-HAL-true,8-MX_USART2_UART_Init-USART2-false-HAL-true
RCC.ADCFreqValue=36000000
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=72000000
RCC.VCOOutput2Freq_Value=8000000
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_256
SPI1.CalculateBaudRate=281.25 KBits/s
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,BaudRatePrescaler,CalculateBaudRate
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
TIM2.IPParameters=Prescaler,Period
TIM2.Period=10
TIM2.Prescaler=7200-1
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_SYNC
VP_RTC_VS_RTC_Activate.Mode=RTC_Enabled
VP_RTC_VS_RTC_Activate.Signal=RTC_VS_RTC_Activate
VP_SYS_VS_ND.Mode=No_Debug
VP_SYS_VS_ND.Signal=SYS_VS_ND
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
board=custom
