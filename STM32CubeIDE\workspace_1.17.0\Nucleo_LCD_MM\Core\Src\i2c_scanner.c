#include "stm32f1xx_hal.h"
#include "i2c_scanner.h"
#include <string.h>
#include <stdio.h>

uint8_t I2C_ScanAndShow(I2C_HandleTypeDef *hi2c, uint8_t *ds3231_found, const char* bus_label) {
    char addr_str[33] = {0};
    uint8_t addr_count = 0;
    *ds3231_found = 0;
    LCD_Clear();
    LCD_Print(0, 0, bus_label);
    HAL_Delay(1000);
    for (uint8_t addr = 1; addr < 127; addr++) {
        if (HAL_I2C_IsDeviceReady(hi2c, addr << 1, 1, 100) == HAL_OK) {
            char hex[3];
            snprintf(hex, sizeof(hex), "%02X", addr);
            strcat(addr_str, hex);
            addr_count++;
            if (addr_count == 8) strcat(addr_str, " ");
            if (addr == 0x68) *ds3231_found = 1;
        }
    }
    LCD_Clear();
    if (addr_count > 0) {
        if (addr_count <= 2) {
            // Show each address on a separate line
            char line1[17] = {0}, line2[17] = {0};
            strncpy(line1, addr_str, 2);
            if (addr_count == 2) strncpy(line2, addr_str + 2, 2);
            LCD_Print(0, 0, line1);
            LCD_Print(1, 0, line2);
        } else {
            char spaced_str[33] = {0};
            for (uint8_t i = 0, j = 0; i < strlen(addr_str); i += 2, j += 3) {
                spaced_str[j] = addr_str[i];
                spaced_str[j+1] = addr_str[i+1];
                spaced_str[j+2] = ' ';
            }
            char line1[17] = {0}, line2[17] = {0};
            strncpy(line1, spaced_str, 16);
            if (strlen(spaced_str) > 16) strncpy(line2, spaced_str + 16, 16);
            LCD_Print(0, 0, line1);
            LCD_Print(1, 0, line2);
        }
        HAL_Delay(2000);
    } else {
        LCD_Print(0, 0, "No I2C devices");
        HAL_Delay(2000);
    }
    LCD_Clear();
    return addr_count;
} 
