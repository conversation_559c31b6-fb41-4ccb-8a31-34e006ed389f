#include "spi1_loopback.h"
#include "sdcard.h" // For SD_Debug_Print
#include <stdio.h>
extern SPI_HandleTypeDef hspi1;

void SPI1_Loopback_Test(void) {
    uint8_t tx = 0xA5;
    uint8_t rx = 0x00;

    // Connect PA7 (MOSI) to PA6 (MISO) with a jumper wire for this test!
    HAL_StatusTypeDef status = HAL_SPI_TransmitReceive(&hspi1, &tx, &rx, 1, 100);

    char dbg[64];
    snprintf(dbg, sizeof(dbg), "SPI1 Loopback sent: 0x%02X, received: 0x%02X, status: %d\r\n", tx, rx, status);
    SD_Debug_Print(dbg);

    if (rx == tx && status == HAL_OK) {
        SD_Debug_Print("SPI1 loopback SUCCESS!\r\n");
    } else {
        SD_Debug_Print("SPI1 loopback FAILED!\r\n");
    }
} 
