#include <SPI.h>
#include <LoRa.h>
#include <Wire.h>
#include <LiquidCrystal_I2C.h>

LiquidCrystal_I2C lcd(0x27, 16, 2); // Adjust address and size as needed

#define LORA_CS_PIN   A1   // PA1
#define LORA_RST_PIN  A3   // PB0
#define LORA_IRQ_PIN  A2   // PA4
int counter = 0;

void printToLCD(const char* msg, uint8_t row = 0, uint8_t col = 0) {
  lcd.setCursor(col, row);
  lcd.print("                "); // Clear the line
  lcd.setCursor(col, row);
  lcd.print(msg);
}

void setup() {
  Serial.begin(115200);
  //clear the screen of serial monitor using clear screen command
  Serial.println("\x1B[2J\x1B[H");

  while (!Serial);

  lcd.init();
  lcd.backlight();
  lcd.setCursor(0, 0);
  lcd.print("LoRa Sender");

  // SPI debug
  pinMode(MISO, INPUT_PULLUP);
  if (digitalRead(MISO) == LOW) {
    Serial.println("DEBUG: MISO stuck LOW - check LoRa power and wiring!");
  } else {
    Serial.println("DEBUG: MISO OK (HIGH)");
  }

  // Manual version register read
  pinMode(LORA_CS_PIN, OUTPUT);
  digitalWrite(LORA_CS_PIN, HIGH);
  SPI.begin();

  delay(100);

  digitalWrite(LORA_CS_PIN, LOW);
  SPI.transfer(0x42 & 0x7F); // Read version register
  uint8_t version = SPI.transfer(0x00);
  digitalWrite(LORA_CS_PIN, HIGH);

  Serial.print("Direct SPI version: 0x");
  Serial.println(version, HEX);

  LoRa.setPins(LORA_CS_PIN, LORA_RST_PIN, LORA_IRQ_PIN);
  LoRa.setSPIFrequency(1000000); // Try 1 MHz

  if (!LoRa.begin(867E6)) {
    Serial.println("Starting LoRa failed!");
    printToLCD("Starting LoRa failed!", 1, 0);
    while (1);
  }
  Serial.println("LoRa init OK!");
  printToLCD("LoRa init OK!", 1, 0);
}

void loop() {
  lcd.setCursor(0, 1);
  lcd.print("Sending...");

  Serial.print("Sending packet: ");
  Serial.println(millis());
  printToLCD(String(millis()).c_str(), 1, 0);

  LoRa.beginPacket();
  LoRa.print("hello ");
  printToLCD("hello ", 2, 0);
  LoRa.print(millis());
  LoRa.endPacket();

  delay(5000);
}
