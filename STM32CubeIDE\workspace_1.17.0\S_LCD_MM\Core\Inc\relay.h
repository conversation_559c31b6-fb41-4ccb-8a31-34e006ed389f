#ifndef __RELAY_H
#define __RELAY_H

#include "stm32f1xx_hal.h"
#include "main.h"

// Initializes relay GPIOs (sets as output, default off)
void Relay_Init(void);

// Relay control functions (active low)
void Relay_On(uint8_t relay_num);    // relay_num: 1-4
void Relay_Off(uint8_t relay_num);   // relay_num: 1-4
void Relay_Toggle(uint8_t relay_num);// relay_num: 1-4
uint8_t Relay_GetState(uint8_t relay_num); // returns 1 if ON, 0 if OFF

#endif // __RELAY_H 