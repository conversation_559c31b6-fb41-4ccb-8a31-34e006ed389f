#ifndef __LED_H
#define __LED_H

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32f1xx_hal.h"
#include <stdint.h>

// LED Pin Definitions
#define LED1_PIN        GPIO_PIN_1
#define LED1_GPIO_PORT  GPIOA

#define LED2_PIN        GPIO_PIN_2
#define LED2_GPIO_PORT  GPIOA

#define LED3_PIN        GPIO_PIN_3
#define LED3_GPIO_PORT  GPIOA

// Switch Pin Definitions
#define SWITCH1_PIN     GPIO_PIN_4     // PA4
#define SWITCH2_PIN     GPIO_PIN_5     // PA5
#define SWITCH3_PIN     GPIO_PIN_6     // PA6
#define SWITCH4_PIN     GPIO_PIN_7     // PA7
#define SWITCH5_PIN     GPIO_PIN_0     // PB0
#define SWITCH6_PIN     GPIO_PIN_1     // PB1
#define SWITCH7_PIN     GPIO_PIN_2     // PB2
#define SWITCH8_PIN     GPIO_PIN_3     // PB3

#define SWITCH_A_PORT   GPIOA          // For switches 1-4
#define SWITCH_B_PORT   GPIOB          // For switches 5-8

// LED control function prototypes
void LED_Init(void);
void LED_Process(void);
void LED_SwitchControl(void);
void LED3_Blink(void);  // Function to handle LED3 blinking
void LED_Init_PA15(void);
void LED_On_PA15(void);
void LED_Off_PA15(void);
void LED_Toggle_PA15(void);

// LED states
#define LED_OFF 0
#define LED_ON  1

#ifdef __cplusplus
}
#endif

#endif /* __LED_H */ 