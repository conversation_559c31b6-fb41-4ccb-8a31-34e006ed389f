#include "lcd_i2c.h"
#include "i2c_manager.h"

static I2C_HandleTypeDef *lcd_hi2c;
static uint8_t LCD_Backlight = LCD_BL;

// Private function prototypes
static void LCD_WriteCommand(uint8_t cmd);
static void LCD_WriteData(uint8_t data);
static void LCD_Write4Bits(uint8_t nibble);
static void LCD_PulseEnable(uint8_t data);
static void LCD_Send(uint8_t data);
static void LCD_Delay(uint32_t delay);

void LCD_Init(void)
{
    // Use I2C1 for LCD as per schematic (PB6=SCL, PB7=SDA)
    lcd_hi2c = I2C_Manager_GetHandle(1);
    
    // Wait for power-up
    HAL_Delay(50);
    
    // Send initialize sequence
    LCD_Write4Bits(0x03);
    HAL_Delay(5);
    LCD_Write4Bits(0x03);
    HAL_Delay(5);
    LCD_Write4Bits(0x03);
    HAL_Delay(1);
    LCD_Write4Bits(0x02);  // Set to 4-bit mode
    
    // Initialize display
    LCD_WriteCommand(LCD_FUNC_SET);     // 4-bit mode, 2 lines, 5x8 font
    LCD_WriteCommand(LCD_DISPLAY_ON);    // Display ON, cursor OFF, blink OFF
    LCD_WriteCommand(LCD_CLEAR);         // Clear display
    HAL_Delay(2);
    LCD_WriteCommand(LCD_ENTRY_MODE);    // Increment cursor, no display shift
    
    LCD_BacklightOn();
}

void LCD_Clear(void)
{
    LCD_WriteCommand(LCD_CLEAR);
    HAL_Delay(2);
}

void LCD_Home(void)
{
    LCD_WriteCommand(LCD_HOME);
    HAL_Delay(2);
}

void LCD_SetCursor(uint8_t row, uint8_t col)
{
    uint8_t row_offsets[] = {0x00, 0x40, 0x14, 0x54};
    if (row >= 4) {
        row = 3; // Ensure row is within bounds
    }
    LCD_WriteCommand(LCD_SET_CURSOR | (col + row_offsets[row]));
}

void LCD_WriteChar(char ch)
{
    LCD_WriteData(ch);
}

void LCD_WriteString(const char* str)
{
    while(*str) LCD_WriteData(*str++);
}

void LCD_WriteWelcomeMessage(void)
{
    LCD_Clear();
    LCD_SetCursor(1, 4);  // Center on second line
    LCD_WriteString("Welcome to");
    LCD_SetCursor(2, 2);  // Center on third line
    LCD_WriteString("Digital LCD MM");
    HAL_Delay(3000);      // Show welcome message for 3 seconds
    LCD_Clear();          // Clear after welcome
}

void LCD_BacklightOn(void)
{
    LCD_Backlight = LCD_BL;
    LCD_Send(0);
}

void LCD_BacklightOff(void)
{
    LCD_Backlight = 0;
    LCD_Send(0);
}

static void LCD_WriteCommand(uint8_t cmd)
{
    // RS = 0 for command
    LCD_Write4Bits(cmd >> 4);    // Send high nibble
    LCD_Write4Bits(cmd & 0x0F);  // Send low nibble
}

static void LCD_WriteData(uint8_t data)
{
    // RS = 1 for data
    LCD_Write4Bits((data >> 4) | LCD_RS);    // Send high nibble
    LCD_Write4Bits((data & 0x0F) | LCD_RS);  // Send low nibble
}

static void LCD_Write4Bits(uint8_t nibble)
{
    nibble = (nibble << 4);  // Shift to upper nibble (D4-D7)
    LCD_Send(nibble | LCD_Backlight);
    LCD_PulseEnable(nibble | LCD_Backlight);
}

static void LCD_PulseEnable(uint8_t data)
{
    LCD_Send(data | LCD_EN);     // Enable high
    HAL_Delay(1);                // Wait
    LCD_Send(data & ~LCD_EN);    // Enable low
    HAL_Delay(1);                // Wait
}

static void LCD_Send(uint8_t data)
{
    if (HAL_I2C_Master_Transmit(lcd_hi2c, LCD_I2C_ADDR, &data, 1, 100) != HAL_OK) {
        // Blink LED3 three times to indicate LCD I2C error
        for (int i = 0; i < 3; ++i) {
            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_3, GPIO_PIN_SET);
            HAL_Delay(200);
            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_3, GPIO_PIN_RESET);
            HAL_Delay(200);
        }
        while(1); // Stop execution for clear debug
    }
}
