#ifndef LCD_I2C_H
#define LCD_I2C_H

#include "stm32f1xx_hal.h"
#include <stdint.h>

// PCF8574 address (0x27 left shifted by 1, matches schematic)
#define LCD_I2C_ADDR    0x4E

// LCD dimensions
#define LCD_ROWS        4
#define LCD_COLS        20

// Commands
#define LCD_CLEAR       0x01
#define LCD_HOME        0x02
#define LCD_ENTRY_MODE  0x06  // Increment cursor, no display shift
#define LCD_DISPLAY_ON  0x0C  // Display on, cursor off, blink off
#define LCD_FUNC_SET    0x28  // 4-bit mode, 2 lines, 5x8 font
#define LCD_SET_CURSOR  0x80

// PCF8574 pins configuration
#define LCD_RS          (1 << 0)   // Register select
#define LCD_RW          (1 << 1)   // Read/Write
#define LCD_EN          (1 << 2)   // Enable
#define LCD_BL          (1 << 3)   // Backlight
#define LCD_D4          (1 << 4)
#define LCD_D5          (1 << 5)
#define LCD_D6          (1 << 6)
#define LCD_D7          (1 << 7)

// Function prototypes
void LCD_Init(void);
void LCD_Clear(void);
void LCD_Home(void);
void LCD_SetCursor(uint8_t row, uint8_t col);
void LCD_WriteChar(char ch);
void LCD_WriteString(const char* str);
void LCD_WriteWelcomeMessage(void);
void LCD_BacklightOn(void);
void LCD_BacklightOff(void);
void LCD_Print(uint8_t row, uint8_t col, const char* msg);

#endif /* LCD_I2C_H */ 