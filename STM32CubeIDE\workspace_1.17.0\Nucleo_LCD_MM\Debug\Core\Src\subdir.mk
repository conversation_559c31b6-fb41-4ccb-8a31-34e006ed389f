################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (12.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../Core/Src/delay_dwt.c \
../Core/Src/diskio.c \
../Core/Src/ds1307.c \
../Core/Src/ds3231.c \
../Core/Src/ff.c \
../Core/Src/ffunicode.c \
../Core/Src/i2c_manager.c \
../Core/Src/i2c_scanner.c \
../Core/Src/lcd_i2c.c \
../Core/Src/led.c \
../Core/Src/lora.c \
../Core/Src/main.c \
../Core/Src/relay.c \
../Core/Src/rtc_display.c \
../Core/Src/sdcard.c \
../Core/Src/spi1_loopback.c \
../Core/Src/stm32f1xx_hal_msp.c \
../Core/Src/stm32f1xx_it.c \
../Core/Src/syscalls.c \
../Core/Src/sysmem.c \
../Core/Src/system_stm32f1xx.c \
../Core/Src/temperature_sensor.c 

OBJS += \
./Core/Src/delay_dwt.o \
./Core/Src/diskio.o \
./Core/Src/ds1307.o \
./Core/Src/ds3231.o \
./Core/Src/ff.o \
./Core/Src/ffunicode.o \
./Core/Src/i2c_manager.o \
./Core/Src/i2c_scanner.o \
./Core/Src/lcd_i2c.o \
./Core/Src/led.o \
./Core/Src/lora.o \
./Core/Src/main.o \
./Core/Src/relay.o \
./Core/Src/rtc_display.o \
./Core/Src/sdcard.o \
./Core/Src/spi1_loopback.o \
./Core/Src/stm32f1xx_hal_msp.o \
./Core/Src/stm32f1xx_it.o \
./Core/Src/syscalls.o \
./Core/Src/sysmem.o \
./Core/Src/system_stm32f1xx.o \
./Core/Src/temperature_sensor.o 

C_DEPS += \
./Core/Src/delay_dwt.d \
./Core/Src/diskio.d \
./Core/Src/ds1307.d \
./Core/Src/ds3231.d \
./Core/Src/ff.d \
./Core/Src/ffunicode.d \
./Core/Src/i2c_manager.d \
./Core/Src/i2c_scanner.d \
./Core/Src/lcd_i2c.d \
./Core/Src/led.d \
./Core/Src/lora.d \
./Core/Src/main.d \
./Core/Src/relay.d \
./Core/Src/rtc_display.d \
./Core/Src/sdcard.d \
./Core/Src/spi1_loopback.d \
./Core/Src/stm32f1xx_hal_msp.d \
./Core/Src/stm32f1xx_it.d \
./Core/Src/syscalls.d \
./Core/Src/sysmem.d \
./Core/Src/system_stm32f1xx.d \
./Core/Src/temperature_sensor.d 


# Each subdirectory must supply rules for building sources it contributes
Core/Src/%.o Core/Src/%.su Core/Src/%.cyclo: ../Core/Src/%.c Core/Src/subdir.mk
	arm-none-eabi-gcc "$<" -mcpu=cortex-m3 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F103xB -c -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I"C:/Users/<USER>/Desktop/iot project/Git_OTA/aunoaInternAniket/STM32CubeIDE/workspace_1.17.0/Nucleo_LCD_MM/Middlewares/FatFs" -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfloat-abi=soft -mthumb -o "$@"

clean: clean-Core-2f-Src

clean-Core-2f-Src:
	-$(RM) ./Core/Src/delay_dwt.cyclo ./Core/Src/delay_dwt.d ./Core/Src/delay_dwt.o ./Core/Src/delay_dwt.su ./Core/Src/diskio.cyclo ./Core/Src/diskio.d ./Core/Src/diskio.o ./Core/Src/diskio.su ./Core/Src/ds1307.cyclo ./Core/Src/ds1307.d ./Core/Src/ds1307.o ./Core/Src/ds1307.su ./Core/Src/ds3231.cyclo ./Core/Src/ds3231.d ./Core/Src/ds3231.o ./Core/Src/ds3231.su ./Core/Src/ff.cyclo ./Core/Src/ff.d ./Core/Src/ff.o ./Core/Src/ff.su ./Core/Src/ffunicode.cyclo ./Core/Src/ffunicode.d ./Core/Src/ffunicode.o ./Core/Src/ffunicode.su ./Core/Src/i2c_manager.cyclo ./Core/Src/i2c_manager.d ./Core/Src/i2c_manager.o ./Core/Src/i2c_manager.su ./Core/Src/i2c_scanner.cyclo ./Core/Src/i2c_scanner.d ./Core/Src/i2c_scanner.o ./Core/Src/i2c_scanner.su ./Core/Src/lcd_i2c.cyclo ./Core/Src/lcd_i2c.d ./Core/Src/lcd_i2c.o ./Core/Src/lcd_i2c.su ./Core/Src/led.cyclo ./Core/Src/led.d ./Core/Src/led.o ./Core/Src/led.su ./Core/Src/lora.cyclo ./Core/Src/lora.d ./Core/Src/lora.o ./Core/Src/lora.su ./Core/Src/main.cyclo ./Core/Src/main.d ./Core/Src/main.o ./Core/Src/main.su ./Core/Src/relay.cyclo ./Core/Src/relay.d ./Core/Src/relay.o ./Core/Src/relay.su ./Core/Src/rtc_display.cyclo ./Core/Src/rtc_display.d ./Core/Src/rtc_display.o ./Core/Src/rtc_display.su ./Core/Src/sdcard.cyclo ./Core/Src/sdcard.d ./Core/Src/sdcard.o ./Core/Src/sdcard.su ./Core/Src/spi1_loopback.cyclo ./Core/Src/spi1_loopback.d ./Core/Src/spi1_loopback.o ./Core/Src/spi1_loopback.su ./Core/Src/stm32f1xx_hal_msp.cyclo ./Core/Src/stm32f1xx_hal_msp.d ./Core/Src/stm32f1xx_hal_msp.o ./Core/Src/stm32f1xx_hal_msp.su ./Core/Src/stm32f1xx_it.cyclo ./Core/Src/stm32f1xx_it.d ./Core/Src/stm32f1xx_it.o ./Core/Src/stm32f1xx_it.su ./Core/Src/syscalls.cyclo ./Core/Src/syscalls.d ./Core/Src/syscalls.o ./Core/Src/syscalls.su ./Core/Src/sysmem.cyclo ./Core/Src/sysmem.d ./Core/Src/sysmem.o ./Core/Src/sysmem.su ./Core/Src/system_stm32f1xx.cyclo ./Core/Src/system_stm32f1xx.d ./Core/Src/system_stm32f1xx.o ./Core/Src/system_stm32f1xx.su ./Core/Src/temperature_sensor.cyclo ./Core/Src/temperature_sensor.d ./Core/Src/temperature_sensor.o ./Core/Src/temperature_sensor.su

.PHONY: clean-Core-2f-Src

