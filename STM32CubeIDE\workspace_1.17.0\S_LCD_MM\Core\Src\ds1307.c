#include "ds1307.h"
#include "i2c_manager.h"

void DS1307_Init(void)
{
    // No special init required for DS1307
}

void DS1307_GetTime(DS1307_TimeTypeDef *time) {
    uint8_t buffer[7];
    I2C_HandleTypeDef* hi2c = I2C_Manager_GetHandle(2);
    HAL_I2C_Mem_Read(hi2c, 0xD0, 0x00, 1, buffer, 7, 1000);
    time->seconds = ((buffer[0] >> 4) * 10) + (buffer[0] & 0x0F);
    time->minutes = ((buffer[1] >> 4) * 10) + (buffer[1] & 0x0F);
    time->hours   = ((buffer[2] >> 4) * 10) + (buffer[2] & 0x0F);
    time->day     = buffer[3];
    time->date    = buffer[4];
    time->month   = buffer[5];
    time->year    = buffer[6];
}

void DS1307_SetTime(DS1307_TimeTypeDef *time) {
    uint8_t buffer[8];
    I2C_HandleTypeDef* hi2c = I2C_Manager_GetHandle(2);
    buffer[0] = ((time->seconds / 10) << 4) | (time->seconds % 10);
    buffer[1] = ((time->minutes / 10) << 4) | (time->minutes % 10);
    buffer[2] = ((time->hours / 10) << 4) | (time->hours % 10);
    buffer[3] = time->day;
    buffer[4] = time->date;
    buffer[5] = time->month;
    buffer[6] = time->year;
    HAL_I2C_Mem_Write(hi2c, 0xD0, 0x00, 1, buffer, 7, 1000);
} 