#include "main.h"
#include "lora.h"
#include <string.h>
#include <stdio.h>
#include "sdcard.h"

extern SPI_HandleTypeDef hspi1; // SD card and LoRa share SPI1

static void LORA_Select(void)   { HAL_GPIO_WritePin(LORA_NSS_GPIO_Port, LORA_NSS_Pin, GPIO_PIN_RESET); }
static void LORA_Unselect(void) { HAL_GPIO_WritePin(LORA_NSS_GPIO_Port, LORA_NSS_Pin, GPIO_PIN_SET); }
static void LORA_Reset(void) {
    HAL_GPIO_WritePin(LORA_RESET_GPIO_Port, LORA_RESET_Pin, GPIO_PIN_RESET);
    HAL_Delay(1);
    HAL_GPIO_WritePin(LORA_RESET_GPIO_Port, LORA_RESET_Pin, GPIO_PIN_SET);
    HAL_Delay(10);
}

static uint8_t LORA_ReadReg(uint8_t addr) {
    uint8_t tx[2] = { addr & 0x7F, 0x00 };
    uint8_t rx[2] = {0};
    LORA_Select();
    HAL_SPI_TransmitReceive(&hspi1, tx, rx, 2, 100);
    LORA_Unselect();
    return rx[1];
}

static void LORA_WriteReg(uint8_t addr, uint8_t value) {
    uint8_t tx[2] = { (addr | 0x80), value };
    LORA_Select();
    HAL_SPI_Transmit(&hspi1, tx, 2, 100);
    LORA_Unselect();
}

void LORA_SetMode(uint8_t mode) {
    uint8_t op = LORA_ReadReg(REG_OP_MODE);
    op = (op & 0xF8) | (mode & 0x07);
    LORA_WriteReg(REG_OP_MODE, op);
    HAL_Delay(2);
}

void LORA_SetFrequency(uint32_t freq_hz) {
    uint64_t frf = ((uint64_t)freq_hz << 19) / 32000000;
    LORA_WriteReg(REG_FRF_MSB, (uint8_t)(frf >> 16));
    LORA_WriteReg(REG_FRF_MID, (uint8_t)(frf >> 8));
    LORA_WriteReg(REG_FRF_LSB, (uint8_t)(frf));
}

void LORA_SetTxPower(int8_t power_dbm) {
    uint8_t pa = 0x80 | (power_dbm > 17 ? 0x0F : (power_dbm < 2 ? 0x02 : (power_dbm - 2)));
    LORA_WriteReg(REG_PA_CONFIG, pa);
}

void LORA_SetSpreadingFactor(uint8_t sf) {
    if (sf < 6) sf = 6;
    if (sf > 12) sf = 12;
    uint8_t mc2 = LORA_ReadReg(REG_MODEM_CONFIG_2);
    mc2 = (mc2 & 0x0F) | ((sf << 4) & 0xF0);
    LORA_WriteReg(REG_MODEM_CONFIG_2, mc2);
}

void LORA_SetBandwidth(uint8_t bw) {
    uint8_t mc1 = LORA_ReadReg(REG_MODEM_CONFIG_1);
    mc1 = (mc1 & 0x0F) | ((bw << 4) & 0xF0);
    LORA_WriteReg(REG_MODEM_CONFIG_1, mc1);
}

void LORA_SetCodingRate(uint8_t cr) {
    uint8_t mc1 = LORA_ReadReg(REG_MODEM_CONFIG_1);
    mc1 = (mc1 & 0xF1) | ((cr << 1) & 0x0E);
    LORA_WriteReg(REG_MODEM_CONFIG_1, mc1);
}

void LORA_SetSyncWord(uint8_t sw) {
    LORA_WriteReg(REG_SYNC_WORD, sw);
}

bool LORA_IsChannelFree(void) {
    // Simple check: RSSI wideband below threshold
    return (LORA_ReadReg(REG_RSSI_WIDEBAND) < 0x40);
}

static uint8_t lora_present = 0;

void LORA_Init(void) {
    LORA_Unselect();
    LORA_Reset();
    HAL_Delay(50);
    uint8_t ver = LORA_ReadReg(0x42);
    char dbg[32];
    snprintf(dbg, sizeof(dbg), "LORA VER: 0x%02X", ver);
    SD_Debug_Print(dbg);
    if (ver == 0x12) {
        lora_present = 1;
    } else {
        lora_present = 0;
        SD_Debug_Print("LORA ERR: Not detected!\r\n");
    }
    // Set LoRa mode
    LORA_WriteReg(REG_OP_MODE, 0x80); // LoRa + sleep
    HAL_Delay(10);
    LORA_SetMode(LORA_MODE_STDBY);
    LORA_SetFrequency(867000000); // Set for 867 MHz band
    LORA_SetTxPower(17); // Max power
    LORA_SetSpreadingFactor(7); // SF7
    LORA_SetBandwidth(7); // 125kHz
    LORA_SetCodingRate(1); // 4/5
    LORA_SetSyncWord(0x34); // Default sync word
    // Set FIFO base addresses
    LORA_WriteReg(REG_FIFO_TX_BASE_ADDR, 0x00);
    LORA_WriteReg(REG_FIFO_RX_BASE_ADDR, 0x00);
    // Clear IRQs
    LORA_WriteReg(REG_IRQ_FLAGS, 0xFF);
}

LORA_StatusTypeDef LORA_Send(uint8_t* data, uint8_t len, uint32_t timeout) {
    if (!lora_present) {
        SD_Debug_Print("LORA ERR: No chip!\r\n");
        return LORA_ERROR;
    }
    LORA_SetMode(LORA_MODE_STDBY);
    LORA_WriteReg(REG_FIFO_ADDR_PTR, 0x00);
    for (uint8_t i = 0; i < len; ++i) {
        LORA_WriteReg(REG_FIFO, data[i]);
    }
    LORA_WriteReg(REG_PAYLOAD_LENGTH, len);
    LORA_WriteReg(REG_IRQ_FLAGS, 0xFF); // Clear IRQs
    LORA_SetMode(LORA_MODE_TX);
    uint32_t start = HAL_GetTick();
    while (HAL_GetTick() - start < timeout) {
        uint8_t irq = LORA_ReadReg(REG_IRQ_FLAGS);
        if (irq & 0x08) { // TX done
            LORA_WriteReg(REG_IRQ_FLAGS, 0x08); // Clear TX done
            SD_Debug_Print("LORA TX OK\r\n");
            return LORA_OK;
        }
    }
    SD_Debug_Print("LORA TX ERR, resetting...\r\n");
    // Reset and re-init LoRa, then retry once
    LORA_Reset();
    HAL_Delay(10);
    LORA_Init();
    LORA_SetMode(LORA_MODE_TX);
    // ... reload FIFO and retry ...
    start = HAL_GetTick();
    while (HAL_GetTick() - start < timeout) {
        uint8_t irq = LORA_ReadReg(REG_IRQ_FLAGS);
        if (irq & 0x08) {
            LORA_WriteReg(REG_IRQ_FLAGS, 0x08);
            SD_Debug_Print("LORA TX OK after reset\r\n");
            return LORA_OK;
        }
    }
    SD_Debug_Print("LORA TX FAIL\r\n");
    return LORA_ERROR;
}

LORA_StatusTypeDef LORA_Receive(uint8_t* buffer, uint8_t maxlen, uint32_t timeout, uint8_t* out_len) {
    if (!lora_present) {
        SD_Debug_Print("LORA ERR: No chip!\r\n");
        return LORA_ERROR;
    }
    LORA_SetMode(LORA_MODE_RXCONTINUOUS);
    uint32_t start = HAL_GetTick();
    while (HAL_GetTick() - start < timeout) {
        uint8_t irq = LORA_ReadReg(REG_IRQ_FLAGS);
        if (irq & 0x40) { // RX done
            LORA_WriteReg(REG_IRQ_FLAGS, 0x40); // Clear RX done
            uint8_t len = LORA_ReadReg(REG_RX_NB_BYTES);
            if (len > maxlen) len = maxlen;
            LORA_WriteReg(REG_FIFO_ADDR_PTR, LORA_ReadReg(REG_FIFO_RX_CURRENT_ADDR));
            for (uint8_t i = 0; i < len; ++i) {
                buffer[i] = LORA_ReadReg(REG_FIFO);
            }
            if (out_len) *out_len = len;
            SD_Debug_Print("LORA RX OK\r\n");
            char dbg[64];
            snprintf(dbg, sizeof(dbg), "LORA RX DATA: %s\r\n", buffer);
            SD_Debug_Print(dbg);
            return LORA_OK;
        }
    }
    SD_Debug_Print("LORA RX TO\r\n");
    return LORA_TIMEOUT;
}

void LORA_DIO0_IRQHandler(void) {
    // This should be called from EXTI IRQ for PA4 (EXTI4)
    // Typically, check IRQ flags and handle RX/TX done
    uint8_t irq = LORA_ReadReg(REG_IRQ_FLAGS);
    if (irq & IRQ_RX_DONE_MASK) {
        // Handle received packet (user should call LORA_Receive)
        LORA_WriteReg(REG_IRQ_FLAGS, IRQ_RX_DONE_MASK);
    }
    if (irq & IRQ_TX_DONE_MASK) {
        // Handle TX done
        LORA_WriteReg(REG_IRQ_FLAGS, IRQ_TX_DONE_MASK);
    }
} 
 