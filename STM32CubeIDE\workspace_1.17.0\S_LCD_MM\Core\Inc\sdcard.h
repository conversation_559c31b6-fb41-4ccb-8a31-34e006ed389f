#ifndef SDCARD_H
#define SDCARD_H

#include "stm32f1xx_hal.h"
#include "stm32f1xx_hal_gpio.h"
#include <stdint.h>
#include <stdbool.h>

// User must call SD_Card_Init with SPI2 handle and CS pin info
bool SD_Card_Init(SPI_HandleTypeDef *hspi, GPIO_TypeDef *cs_port, uint16_t cs_pin);
bool SD_Card_ReadBlock(uint32_t block_addr, uint8_t *buffer);
bool SD_Card_WriteBlock(uint32_t block_addr, const uint8_t *buffer);
void SD_Debug_Print(const char* msg);
void SD_Save_Latest_Record(float temp1, float temp2, float temp3, float temp4, uint8_t year, uint8_t month, uint8_t date, uint8_t hours, uint8_t minutes, uint8_t seconds);
void SD_Read_Latest_Record(char *buffer, uint16_t bufsize);

#endif // SDCARD_H 