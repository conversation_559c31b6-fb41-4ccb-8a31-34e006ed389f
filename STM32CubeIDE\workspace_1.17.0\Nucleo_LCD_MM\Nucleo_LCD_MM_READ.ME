# Nucleo_LCD_MM: STM32F103RB IOT Project

## Overview

This project is a modular IOT demonstration for the STM32 Nucleo-F103RB board, featuring:

- **I2C LCD (PCF8574T backpack) display**
- **RTC (DS3231, with legacy DS1307 support)**
- **Temperature sensor**
- **SD card logging (via SPI)**
- **User button and LED control**
- **I2C device scanning and robust error/debug feedback**

The code is structured for easy porting, robust debugging, and extensibility. All user code is placed within `/* USER CODE BEGIN */` and `/* USER CODE END */` blocks to ensure compatibility with STM32CubeMX code generation.

---

## Hardware Requirements

- **STM32 Nucleo-F103RB** development board
- **I2C LCD** (16x2 or 20x4, PCF8574T backpack, address 0x27)
- **RTC module** (DS3231 preferred, 0x68)
- **SD card module** (SPI interface)
- **Temperature sensor** (1-wire or analog, see code)
- **Pushbutton** (User button on PC13)
- **LEDs** (Onboard LD2, plus optional external LEDs)
- **Wiring:**  
  - I2C1: PB8 (SCL), PB9 (SDA)  
  - SPI2: SD card  
  - PC13: User button  
  - PA5: LD2 (green LED)

**Note:**  
- Ensure I2C pull-ups (4.7kΩ) are present on SDA/SCL.
- Ground A0/A1/A2 on the PCF8574T LCD backpack for address 0x27.
- Use only pins not reserved by the Nucleo board for user peripherals.

---

## Features

- **I2C Device Scanner:**  
  Scans all I2C addresses at startup, displays found addresses on the LCD for debugging.
- **LCD Display:**  
  Flexible print function (`LCD_Print(row, col, msg)`), welcome message, and runtime status.
- **RTC Integration:**  
  Reads and displays time/date from DS3231. If not found, displays "No RTC".
- **SD Card Logging:**  
  Initializes SD card, logs temperature and time data.
- **Temperature Sensing:**  
  Reads temperature and displays/logs it.
- **User Button:**  
  Triggers time display or other actions.
- **LED Debugging:**  
  LD2 blinks for status; error conditions are signaled via LED and LCD.

---

## Directory Structure

```
Nucleo_LCD_MM/
  ├── Core/
  │   ├── Inc/         # Header files for all modules
  │   └── Src/         # Source files for all modules
  ├── Drivers/         # STM32 HAL and CMSIS drivers
  ├── Middlewares/     # FatFs for SD card
  ├── mkfatimg/        # FAT image creation utility
  └── i2c_scanner/     # Arduino I2C scanner (reference)
```

---

## Software Architecture

### Main Application Flow

- **Initialization:**  
  - HAL, clocks, GPIO, DMA, UART, I2C, SPI, RTC, TIM
  - DWT cycle counter for microsecond delays
  - I2C manager and LCD initialization
  - I2C device scan and display
  - SD card and SIM test (if present)
- **Main Loop:**  
  - Show RTC time on LCD (if found)
  - Blink LD2 for heartbeat
  - Respond to user button

### Key Modules

#### LCD I2C Driver (`lcd_i2c.h/.c`)

- `LCD_Init()`, `LCD_Clear()`, `LCD_SetCursor(row, col)`, `LCD_WriteChar(ch)`, `LCD_WriteString(str)`
- `LCD_Print(row, col, msg)`: Print string at specified position
- `LCD_BacklightOn()`, `LCD_BacklightOff()`
- Pin mapping:  
  - P0=RS, P1=RW, P2=EN, P3=BL, P4=D4, P5=D5, P6=D6, P7=D7

#### RTC Driver (`ds3231.h/.c`)

- `DS3231_Init()`, `DS3231_GetTime(&time)`, `DS3231_SetTime(&time)`
- `Set_RTC_To_Default()`: Helper to set default time
- Struct:  
  ```c
  typedef struct {
      uint8_t seconds, minutes, hours, day, date, month, year;
  } DS3231_TimeTypeDef;
  ```

#### I2C Scanner (`i2c_scanner.h/.c`)

- `I2C_ScanAndShow(&hi2c, &ds3231_found, "I2C1 Scan")`: Scans bus, updates LCD and flag

#### LED Control (`led.h/.c`)

- `LED_Init()`, `LED_Process()`, `LED_SwitchControl()`, `LED3_Blink()`
- Pin defines for up to 3 LEDs and 8 switches

#### SD Card (`sdcard.h/.c`)

- `SD_Card_Init(&hspi2, GPIOB, GPIO_PIN_12)`
- `SD_Card_ReadBlock(addr, buffer)`, `SD_Card_WriteBlock(addr, buffer)`
- `SD_Save_Latest_Record(temp, y, m, d, h, min, s)`
- `SD_Read_Latest_Record(buffer, bufsize)`

#### Temperature Sensor (`temperature_sensor.h/.c`)

- `TemperatureSensor_Init()`
- `TemperatureSensor_GetTemperature()`
- Uses DWT-based microsecond delay

#### I2C Manager (`i2c_manager.h/.c`)

- `I2C_Manager_Init()`
- `I2C_Manager_GetHandle(bus)`

#### RTC Display Helper (`rtc_display.h/.c`)

- `Show_RTC_On_LCD(ds3231_found)`: Prints formatted time/date or "No RTC" on LCD

---

## Pin Mapping

| Function      | STM32 Pin | Nucleo Pin | Notes                |
|---------------|-----------|------------|----------------------|
| I2C1_SCL      | PB8       | D15        | LCD/RTC SCL          |
| I2C1_SDA      | PB9       | D14        | LCD/RTC SDA          |
| I2C2_SCL      | PB10      | PB10       | LCD/RTC SCL          |
| I2C2_SDA      | PB11      | PB11       | LCD/RTC SDA          |
| SPI2_CS       | PB12      | PB12       | SD card CS           |
| SPI2_SCK      | PB13      | PB13       | SD card SCK          |
| SPI2_MISO     | PB14      | PB14       | SD card MISO         |
| SPI2_MOSI     | PB15      | PB15       | SD card MOSI         |
| LD2 (LED)     | PA5       | D13        | Onboard green LED    |
| User Button   | PC13      |            | Onboard button       |

---

## Usage

### 1. Hardware Setup

- Connect I2C LCD and RTC to PB8/PB9 (with pull-ups).
- Connect SD card module to SPI2 pins.
- Ensure all modules are powered (3.3V or 5V as required).
- Ground A0/A1/A2 on LCD backpack for address 0x27.

### 2. Build and Flash

- Open the project in STM32CubeIDE.
- Generate code if you change CubeMX settings (keep user code in `USER CODE` blocks).
- Build and flash to the Nucleo board.

### 3. Operation

- On power-up, the LCD will show I2C addresses found.
- SD card and SIM status are displayed.
- If RTC is found, time/date is shown; otherwise, "No RTC".
- LD2 blinks for heartbeat; errors are shown on LCD and via LED.
- Press the user button to trigger time display or other actions (see code).

---

## Troubleshooting

- **LCD only shows backlight:**  
  - Check I2C address (should be 0x27/0x4E), wiring, and pull-ups.
  - Ensure A0/A1/A2 are grounded.

- **"No RTC" or garbage time:**  
  - Check RTC wiring, address (0x68/0xD0), and power.
  - Use I2C scanner output on LCD to verify device presence.

- **SD card not detected:**  
  - Check SPI wiring and CS pin.
  - Use a known-good, FAT-formatted SD card.

- **No LED blink:**  
  - Check code for `HAL_GPIO_WritePin(LD2_GPIO_Port, LD2_Pin, ...)` calls.

- **CubeMX overwrites code:**  
  - Always place custom code inside `/* USER CODE BEGIN */` and `/* USER CODE END */` blocks.

---

## Extending the Project (Future Additional Content / DLC)

- Add new sensors or peripherals by creating new modules in `Core/Inc` and `Core/Src`.
- Use the I2C manager for additional I2C devices.
- Use the SD card module for data logging.
- Update the LCD display logic for new features.
- LoRa Module 

---

## License

This project is based on STM32CubeMX/HAL code and is provided AS-IS. See the LICENSE file for details.

---

## Additional Softwares Used

- STM32 HAL and CubeMX
- FatFs (SD card middleware)
- PCF8574T LCD backpack and DS3231 RTC open-source drivers

---

**For further details, see the code in `Core/Inc` and `Core/Src`.**

---
