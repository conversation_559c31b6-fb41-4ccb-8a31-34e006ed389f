#include "rtc_display.h"
#include "ds3231.h"
#include "temperature_sensor.h"
#include <stdio.h>
#include <string.h>
#include "sdcard.h"

void Show_AllTemperatures_On_LCD(void) {
    char line1[21], line2[21], line3[21], line4[21];
    float temps[4];
    for (int i = 0; i < 4; i++) {
        temps[i] = TemperatureSensor_GetTemperature(i);
    }
    LCD_Clear();
    snprintf(line1, sizeof(line1), "Temp1: %6.2f C", (temps[0] > -100.0f && temps[0] < 100.0f) ? temps[0] : 0.0f);
    snprintf(line2, sizeof(line2), "Temp2: %6.2f C", (temps[1] > -100.0f && temps[1] < 100.0f) ? temps[1] : 0.0f);
    snprintf(line3, sizeof(line3), "Temp3: %6.2f C", (temps[2] > -100.0f && temps[2] < 100.0f) ? temps[2] : 0.0f);
    snprintf(line4, sizeof(line4), "Temp4: %6.2f C", (temps[3] > -100.0f && temps[3] < 100.0f) ? temps[3] : 0.0f);
    LCD_Print(0, 0, line1);
    LCD_Print(1, 0, line2);
    LCD_Print(2, 0, line3);
    LCD_Print(3, 0, line4);
    // Save backup to SD card (using RTC time if available)
    extern uint8_t ds3231_found;
    if (ds3231_found) {
        DS3231_TimeTypeDef rtcTime;
        DS3231_GetTime(&rtcTime);
        SD_Save_Latest_Record(temps[0], temps[1], temps[2], temps[3], rtcTime.year, rtcTime.month, rtcTime.date, rtcTime.hours, rtcTime.minutes, rtcTime.seconds);
    }
    HAL_Delay(2000);
}

void Show_RTC_On_LCD(uint8_t ds3231_found) {
    if (ds3231_found) {
        DS3231_TimeTypeDef rtcTime;
        DS3231_GetTime(&rtcTime);
        // float rtc_temp = -127.0f;
        // #ifdef DS3231_HAS_TEMP
        // rtc_temp = DS3231_GetTemperature();
        // #endif
        char* days[] = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
        char ampm[3] = "AM";
        uint8_t hour12 = rtcTime.hours;
        if (hour12 == 0) { hour12 = 12; strcpy(ampm, "AM"); }
        else if (hour12 == 12) { strcpy(ampm, "PM"); }
        else if (hour12 > 12) { hour12 -= 12; strcpy(ampm, "PM"); } else { strcpy(ampm, "AM"); }
        char line1[21], line2[21];
        snprintf(line1, sizeof(line1), "%s %02d/%02d/20%02d", days[(rtcTime.day-1)%7], rtcTime.date, rtcTime.month, rtcTime.year);
        snprintf(line2, sizeof(line2), "Time: %02d:%02d:%02d %s", hour12, rtcTime.minutes, rtcTime.seconds, ampm);
        LCD_Clear();
        LCD_Print(0, 0, line1);
        LCD_Print(1, 0, line2);
        // char line3[21];
        // if (rtc_temp > -100.0f && rtc_temp < 100.0f) {
        //     snprintf(line3, sizeof(line3), "RTC Temp: %.2f C", rtc_temp);
        //     LCD_Print(2, 0, line3);
        // } else {
        //     LCD_Print(2, 0, "RTC Temp: --.- C");
        // }
    } else {
        LCD_Clear();
        LCD_Print(0, 0, "No RTC");
        LCD_Print(1, 0, "");
    }
} 

 