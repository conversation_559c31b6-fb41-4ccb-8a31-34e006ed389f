#include "relay.h"

// Helper macro to get GPIO port and pin for a relay number
#define RELAY_PORT(n) \
    ((n)==1 ? RELAY_1_GPIO_Port : \
     (n)==2 ? RELAY_2_GPIO_Port : \
     (n)==3 ? RELAY_3_GPIO_Port : \
     (n)==4 ? RELAY_4_GPIO_Port : 0)
#define RELAY_PIN(n) \
    ((n)==1 ? RELAY_1_Pin : \
     (n)==2 ? RELAY_2_Pin : \
     (n)==3 ? RELAY_3_Pin : \
     (n)==4 ? RELAY_4_Pin : 0)

void Relay_Init(void) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    // Configure all relay pins as output, pull-up, default OFF (HIGH)
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    for (uint8_t i = 1; i <= 4; ++i) {
        GPIO_InitStruct.Pin = RELAY_PIN(i);
        HAL_GPIO_Init(RELAY_PORT(i), &GPIO_InitStruct);
        HAL_GPIO_WritePin(RELAY_PORT(i), RELAY_PIN(i), GPIO_PIN_SET); // OFF
    }
}

void Relay_On(uint8_t relay_num) {
    if (relay_num < 1 || relay_num > 4) return;
    HAL_GPIO_WritePin(RELAY_PORT(relay_num), RELAY_PIN(relay_num), GPIO_PIN_RESET); // ON (active low)
}

void Relay_Off(uint8_t relay_num) {
    if (relay_num < 1 || relay_num > 4) return;
    HAL_GPIO_WritePin(RELAY_PORT(relay_num), RELAY_PIN(relay_num), GPIO_PIN_SET); // OFF
}

void Relay_Toggle(uint8_t relay_num) {
    if (relay_num < 1 || relay_num > 4) return;
    HAL_GPIO_TogglePin(RELAY_PORT(relay_num), RELAY_PIN(relay_num));
}

uint8_t Relay_GetState(uint8_t relay_num) {
    if (relay_num < 1 || relay_num > 4) return 0xFF; // invalid
    // ON if pin is LOW
    return (HAL_GPIO_ReadPin(RELAY_PORT(relay_num), RELAY_PIN(relay_num)) == GPIO_PIN_RESET) ? 1 : 0;
} 