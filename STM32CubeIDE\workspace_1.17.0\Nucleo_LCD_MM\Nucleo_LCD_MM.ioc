#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.I2C1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.I2C1_RX.0.Instance=DMA1_Channel7
Dma.I2C1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.I2C1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.I2C1_RX.0.Mode=DMA_NORMAL
Dma.I2C1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.I2C1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.I2C1_RX.0.Priority=DMA_PRIORITY_LOW
Dma.I2C1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.I2C1_TX.1.Direction=DMA_MEMORY_TO_PERIPH
Dma.I2C1_TX.1.Instance=DMA1_Channel6
Dma.I2C1_TX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.I2C1_TX.1.MemInc=DMA_MINC_ENABLE
Dma.I2C1_TX.1.Mode=DMA_NORMAL
Dma.I2C1_TX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.I2C1_TX.1.PeriphInc=DMA_PINC_DISABLE
Dma.I2C1_TX.1.Priority=DMA_PRIORITY_LOW
Dma.I2C1_TX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.I2C2_RX.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.I2C2_RX.3.Instance=DMA1_Channel5
Dma.I2C2_RX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.I2C2_RX.3.MemInc=DMA_MINC_ENABLE
Dma.I2C2_RX.3.Mode=DMA_NORMAL
Dma.I2C2_RX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.I2C2_RX.3.PeriphInc=DMA_PINC_DISABLE
Dma.I2C2_RX.3.Priority=DMA_PRIORITY_LOW
Dma.I2C2_RX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.I2C2_TX.2.Direction=DMA_MEMORY_TO_PERIPH
Dma.I2C2_TX.2.Instance=DMA1_Channel4
Dma.I2C2_TX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.I2C2_TX.2.MemInc=DMA_MINC_ENABLE
Dma.I2C2_TX.2.Mode=DMA_NORMAL
Dma.I2C2_TX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.I2C2_TX.2.PeriphInc=DMA_PINC_DISABLE
Dma.I2C2_TX.2.Priority=DMA_PRIORITY_LOW
Dma.I2C2_TX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.Request0=I2C1_RX
Dma.Request1=I2C1_TX
Dma.Request2=I2C2_TX
Dma.Request3=I2C2_RX
Dma.RequestsNb=4
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F103RBT6
Mcu.Family=STM32F1
Mcu.IP0=DMA
Mcu.IP1=I2C1
Mcu.IP2=I2C2
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=RTC
Mcu.IP6=SPI1
Mcu.IP7=SYS
Mcu.IP8=TIM2
Mcu.IP9=USART2
Mcu.IPNb=10
Mcu.Name=STM32F103R(8-B)Tx
Mcu.Package=LQFP64
Mcu.Pin0=PC13-TAMPER-RTC
Mcu.Pin1=PC14-OSC32_IN
Mcu.Pin10=PA6
Mcu.Pin11=PA7
Mcu.Pin12=PB0
Mcu.Pin13=PB10
Mcu.Pin14=PB11
Mcu.Pin15=PB12
Mcu.Pin16=PC6
Mcu.Pin17=PC7
Mcu.Pin18=PC8
Mcu.Pin19=PC9
Mcu.Pin2=PC15-OSC32_OUT
Mcu.Pin20=PA8
Mcu.Pin21=PA9
Mcu.Pin22=PA10
Mcu.Pin23=PA11
Mcu.Pin24=PA12
Mcu.Pin25=PA13
Mcu.Pin26=PA14
Mcu.Pin27=PA15
Mcu.Pin28=PB3
Mcu.Pin29=PB8
Mcu.Pin3=PD0-OSC_IN
Mcu.Pin30=PB9
Mcu.Pin31=VP_RTC_VS_RTC_Activate
Mcu.Pin32=VP_RTC_VS_RTC_Calendar
Mcu.Pin33=VP_SYS_VS_Systick
Mcu.Pin34=VP_TIM2_VS_ClockSourceINT
Mcu.Pin4=PD1-OSC_OUT
Mcu.Pin5=PA1
Mcu.Pin6=PA2
Mcu.Pin7=PA3
Mcu.Pin8=PA4
Mcu.Pin9=PA5
Mcu.PinsNb=35
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103RBTx
MxCube.Version=6.13.0
MxDb.Version=DB.6.0.130
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.DMA1_Channel4_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Channel5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Channel6_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Channel7_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.EXTI15_10_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.I2C1_ER_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.I2C1_EV_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.I2C2_ER_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.I2C2_EV_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.RTC_Alarm_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.RTC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.SPI1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:false
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
PA1.GPIOParameters=GPIO_Label
PA1.GPIO_Label=LORA_NSS
PA1.Locked=true
PA1.Signal=GPIO_Output
PA10.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PA10.GPIO_Label=TEMPERATURE_SENSOR_2
PA10.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PA10.GPIO_PuPd=GPIO_NOPULL
PA10.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA10.Locked=true
PA10.Signal=GPIO_Output
PA11.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PA11.GPIO_Label=TEMPERATURE_SENSOR_3
PA11.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PA11.GPIO_PuPd=GPIO_NOPULL
PA11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA11.Locked=true
PA11.Signal=GPIO_Output
PA12.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PA12.GPIO_Label=TEMPERATURE_SENSOR_4
PA12.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PA12.GPIO_PuPd=GPIO_NOPULL
PA12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA12.Locked=true
PA12.Signal=GPIO_Output
PA13.GPIOParameters=GPIO_Label
PA13.GPIO_Label=TMS
PA13.Locked=true
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.GPIOParameters=GPIO_Label
PA14.GPIO_Label=TCK
PA14.Locked=true
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_Label
PA15.GPIO_Label=LD2 [Green Led]
PA15.Locked=true
PA15.Signal=GPIO_Output
PA2.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PA2.GPIO_Label=USART_TX
PA2.GPIO_Mode=GPIO_MODE_AF_PP
PA2.GPIO_PuPd=GPIO_NOPULL
PA2.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA2.Locked=true
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PA3.GPIO_Label=USART_RX
PA3.GPIO_Mode=GPIO_MODE_AF_PP
PA3.GPIO_PuPd=GPIO_NOPULL
PA3.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA3.Locked=true
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=LORA_DIO
PA4.Locked=true
PA4.Signal=GPXTI4
PA5.Mode=Full_Duplex_Master
PA5.Signal=SPI1_SCK
PA6.Mode=Full_Duplex_Master
PA6.Signal=SPI1_MISO
PA7.Mode=Full_Duplex_Master
PA7.Signal=SPI1_MOSI
PA8.GPIOParameters=GPIO_Label
PA8.GPIO_Label=CS_LORA
PA8.Locked=true
PA8.Signal=GPIO_Output
PA9.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PA9.GPIO_Label=TEMPERATURE_SENSOR_1
PA9.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PA9.GPIO_PuPd=GPIO_NOPULL
PA9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA9.Locked=true
PA9.Signal=GPIO_Output
PB0.GPIOParameters=GPIO_Label
PB0.GPIO_Label=LORA_RESET
PB0.Locked=true
PB0.Signal=GPIO_Output
PB10.GPIOParameters=GPIO_Label
PB10.GPIO_Label=SCL_RTC
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.GPIOParameters=GPIO_Label
PB11.GPIO_Label=SDA_RTC
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB12.GPIOParameters=GPIO_Label
PB12.GPIO_Label=CS_SDCARD
PB12.Locked=true
PB12.Signal=GPIO_Output
PB3.GPIOParameters=GPIO_Label
PB3.GPIO_Label=SWO
PB3.Locked=true
PB3.Signal=SYS_JTDO-TRACESWO
PB8.Locked=true
PB8.Mode=I2C
PB8.Signal=I2C1_SCL
PB9.Locked=true
PB9.Mode=I2C
PB9.Signal=I2C1_SDA
PC13-TAMPER-RTC.GPIOParameters=GPIO_PuPd,GPIO_Label
PC13-TAMPER-RTC.GPIO_Label=B1 [Blue PushButton]
PC13-TAMPER-RTC.GPIO_PuPd=GPIO_NOPULL
PC13-TAMPER-RTC.Locked=true
PC13-TAMPER-RTC.Signal=GPXTI13
PC14-OSC32_IN.Locked=true
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Locked=true
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC6.GPIOParameters=GPIO_PuPd,GPIO_Label
PC6.GPIO_Label=RELAY_1
PC6.GPIO_PuPd=GPIO_PULLUP
PC6.Locked=true
PC6.Signal=GPIO_Output
PC7.GPIOParameters=GPIO_PuPd,GPIO_Label
PC7.GPIO_Label=RELAY_2
PC7.GPIO_PuPd=GPIO_PULLUP
PC7.Locked=true
PC7.Signal=GPIO_Output
PC8.GPIOParameters=GPIO_PuPd,GPIO_Label
PC8.GPIO_Label=RELAY_3
PC8.GPIO_PuPd=GPIO_PULLUP
PC8.Locked=true
PC8.Signal=GPIO_Output
PC9.GPIOParameters=GPIO_PuPd,GPIO_Label
PC9.GPIO_Label=RELAY_4
PC9.GPIO_PuPd=GPIO_PULLUP
PC9.Locked=true
PC9.Signal=GPIO_Output
PD0-OSC_IN.Locked=true
PD0-OSC_IN.Mode=HSE-External-Clock-Source
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Locked=true
PD1-OSC_OUT.Mode=HSE-External-Clock-Source
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103RBTx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=Nucleo_LCD_MM.ioc
ProjectManager.ProjectName=Nucleo_LCD_MM
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART2_UART_Init-USART2-false-HAL-true,5-MX_I2C1_Init-I2C1-false-HAL-true,6-MX_TIM2_Init-TIM2-false-HAL-true,7-MX_RTC_Init-RTC-false-HAL-true,8-MX_I2C2_Init-I2C2-false-HAL-true,9-MX_SPI2_Init-SPI2-false-HAL-true
RCC.ADCFreqValue=32000000
RCC.AHBFreq_Value=64000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=32000000
RCC.APB1TimFreq_Value=64000000
RCC.APB2Freq_Value=64000000
RCC.APB2TimFreq_Value=64000000
RCC.FCLKCortexFreq_Value=64000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=64000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,RTCClockSelection,RTCFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=64000000
RCC.PLLCLKFreq_Value=64000000
RCC.PLLMCOFreq_Value=32000000
RCC.PLLMUL=RCC_PLL_MUL16
RCC.RTCClockSelection=RCC_RTCCLKSOURCE_LSE
RCC.RTCFreq_Value=32768
RCC.SYSCLKFreq_VALUE=64000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=64000000
RCC.USBFreq_Value=64000000
RCC.VCOOutput2Freq_Value=4000000
RTC.Date=23
RTC.IPParameters=Date,Month,Year
RTC.Month=RTC_MONTH_JUNE
RTC.Year=25
SH.GPXTI13.0=GPIO_EXTI13
SH.GPXTI13.ConfNb=1
SH.GPXTI4.0=GPIO_EXTI4
SH.GPXTI4.ConfNb=1
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_256
SPI1.CalculateBaudRate=250.0 KBits/s
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,BaudRatePrescaler,CalculateBaudRate
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
TIM2.IPParameters=Prescaler,Period
TIM2.Period=10
TIM2.Prescaler=6400-1
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
VP_RTC_VS_RTC_Activate.Mode=RTC_Enabled
VP_RTC_VS_RTC_Activate.Signal=RTC_VS_RTC_Activate
VP_RTC_VS_RTC_Calendar.Mode=RTC_Calendar
VP_RTC_VS_RTC_Calendar.Signal=RTC_VS_RTC_Calendar
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
board=NUCLEO-F103RB
boardIOC=true
isbadioc=false
