#include "stm32f1xx_hal.h"
#include "sdcard.h"
#include <string.h>
#include "lcd_i2c.h"
#include "main.h" // for SD_Debug_Print
#include "ff.h"
#include "diskio.h"
#include <stdio.h>

extern SPI_HandleTypeDef hspi1;

static SPI_HandleTypeDef *sd_spi = NULL;
static GPIO_TypeDef *sd_cs_port = NULL;
static uint16_t sd_cs_pin = 0;

#define SD_BLOCK_SIZE 512

static void SD_Select(void)   { HAL_GPIO_WritePin(sd_cs_port, sd_cs_pin, GPIO_PIN_RESET); }
static void SD_Deselect(void){ HAL_GPIO_WritePin(sd_cs_port, sd_cs_pin, GPIO_PIN_SET); }

static uint8_t SD_SPI_TxRx(uint8_t data) {
    uint8_t rx;
    HAL_SPI_TransmitReceive(sd_spi, &data, &rx, 1, 100);
    return rx;
}

static void SD_SendDummyClocks(void) {
    SD_Deselect();
    for (int i = 0; i < 10; i++) SD_SPI_TxRx(0xFF);
}

static uint8_t SD_SendCmd(uint8_t cmd, uint32_t arg, uint8_t crc) {
    SD_Deselect();
    SD_SPI_TxRx(0xFF);
    SD_Select();
    SD_SPI_TxRx(0xFF);
    SD_SPI_TxRx(cmd | 0x40);
    SD_SPI_TxRx(arg >> 24);
    SD_SPI_TxRx(arg >> 16);
    SD_SPI_TxRx(arg >> 8);
    SD_SPI_TxRx(arg);
    SD_SPI_TxRx(crc);
    for (int i = 0; i < 8; i++) {
        uint8_t r = SD_SPI_TxRx(0xFF);
        if (!(r & 0x80)) return r;
    }
    return 0xFF;
}

bool SD_Card_Init(SPI_HandleTypeDef *hspi, GPIO_TypeDef *cs_port, uint16_t cs_pin) {
    sd_spi = hspi;
    sd_cs_port = cs_port;
    sd_cs_pin = cs_pin;
    // Print SPI mode (CPOL/CPHA)
    char dbg[64];
    snprintf(dbg, sizeof(dbg), "SD: SPI Mode CPOL=%d, CPHA=%d\r\n", (hspi->Init.CLKPolarity == SPI_POLARITY_HIGH), (hspi->Init.CLKPhase == SPI_PHASE_2EDGE));
    SD_Debug_Print(dbg);
    // Ensure CS is HIGH before dummy clocks
    HAL_GPIO_WritePin(sd_cs_port, sd_cs_pin, GPIO_PIN_SET);
    // Wait for SD card power-up
    HAL_Delay(50);
    SD_Debug_Print("SD: Sending dummy clocks\r\n");
    SD_SendDummyClocks();
    HAL_Delay(2);
    int retry = 0;
    uint8_t resp = 0xFF;
    SD_Debug_Print("SD: Sending CMD0\r\n");
    for (retry = 0; retry < 10; retry++) {
        resp = SD_SendCmd(0, 0, 0x95);
        if (resp == 0x01) break;
        SD_Debug_Print("SD: CMD0 retry\r\n");
        HAL_Delay(5);
    }
    if (resp != 0x01) {
        snprintf(dbg, sizeof(dbg), "SD: CMD0 FAIL r=0x%02X\r\n", resp);
        SD_Debug_Print(dbg);
        if (resp == 0x00) SD_Debug_Print("SD: Card not entering SPI mode!\r\n");
        HAL_Delay(100);
        return false;
    }
    resp = SD_SendCmd(8, 0x1AA, 0x87);
    if (resp != 0x01) { char dbg[32]; snprintf(dbg, sizeof(dbg), "SD: CMD8 FAIL r=0x%02X", resp); SD_Debug_Print(dbg); HAL_Delay(100); return false; }
    int acmd41_ok = 0;
    for (int i = 0; i < 1000; i++) {
        uint8_t r55 = SD_SendCmd(55, 0, 0x65);
        uint8_t r41 = SD_SendCmd(41, 0x40000000, 0x77);
        if (r55 <= 0x01 && r41 == 0x00) { acmd41_ok = 1; break; }
        HAL_Delay(1);
    }
    if (!acmd41_ok) { SD_Debug_Print("SD: ACMD41 FAIL"); HAL_Delay(100); return false; }
    resp = SD_SendCmd(58, 0, 0xFD);
    if (resp != 0x00) { char dbg[32]; snprintf(dbg, sizeof(dbg), "SD: CMD58 FAIL r=0x%02X", resp); SD_Debug_Print(dbg); HAL_Delay(100); return false; }
    SD_Deselect();
    SD_SPI_TxRx(0xFF);
    SD_Debug_Print("SD: INIT OK"); HAL_Delay(100);
    return true;
}

bool SD_Card_ReadBlock(uint32_t block_addr, uint8_t *buffer) {
    if (SD_SendCmd(17, block_addr, 0xFF) != 0x00) return false; // CMD17: READ_SINGLE_BLOCK
    for (int i = 0; i < 10000; i++) {
        if (SD_SPI_TxRx(0xFF) == 0xFE) break;
    }
    for (int i = 0; i < SD_BLOCK_SIZE; i++) buffer[i] = SD_SPI_TxRx(0xFF);
    SD_SPI_TxRx(0xFF); // CRC
    SD_SPI_TxRx(0xFF);
    SD_Deselect();
    SD_SPI_TxRx(0xFF);
    return true;
}

bool SD_Card_WriteBlock(uint32_t block_addr, const uint8_t *buffer) {
    if (SD_SendCmd(24, block_addr, 0xFF) != 0x00) return false; // CMD24: WRITE_BLOCK
    SD_SPI_TxRx(0xFF);
    SD_SPI_TxRx(0xFE);
    for (int i = 0; i < SD_BLOCK_SIZE; i++) SD_SPI_TxRx(buffer[i]);
    SD_SPI_TxRx(0xFF); // CRC
    SD_SPI_TxRx(0xFF);
    uint8_t resp = SD_SPI_TxRx(0xFF);
    if ((resp & 0x1F) != 0x05) { SD_Deselect(); return false; }
    while (SD_SPI_TxRx(0xFF) == 0) ; // Wait for write done
    SD_Deselect();
    SD_SPI_TxRx(0xFF);
    return true;
}

void SD_Save_Latest_Record(float temp1, float temp2, float temp3, float temp4, uint8_t year, uint8_t month, uint8_t date, uint8_t hours, uint8_t minutes, uint8_t seconds) {
    FIL file;
    UINT bw;
    char line[128];
    snprintf(line, sizeof(line),
        "20%02d-%02d-%02d %02d:%02d:%02d\r\nT1: %.2f C\r\nT2: %.2f C\r\nT3: %.2f C\r\nT4: %.2f C\r\n",
        year, month, date, hours, minutes, seconds, temp1, temp2, temp3, temp4);
    if (f_open(&file, "backup.txt", FA_WRITE | FA_CREATE_ALWAYS) == FR_OK) {
        f_write(&file, line, strlen(line), &bw);
        f_close(&file);
    }
}

void SD_Read_Latest_Record(char *buffer, uint16_t bufsize) {
    FIL file;
    UINT br;
    if (f_open(&file, "backup.txt", FA_READ) == FR_OK) {
        f_read(&file, buffer, bufsize-1, &br);
        buffer[br] = 0;
        f_close(&file);
    } else {
        strcpy(buffer, "No backup found");
    }
} 