#include "delay_dwt.h"
#include "stm32f1xx_hal.h"

// Initialize DWT for cycle counting
void delay_dwt_init(void) {
    CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
    DWT->CYCCNT = 0;
    DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
}

// Microsecond delay using DWT cycle counter
void delay_us(uint32_t us) {
    uint32_t cycles_per_us = HAL_RCC_GetHCLKFreq() / 1000000;
    uint32_t start = DWT->CYCCNT;
    uint32_t delay_cycles = us * cycles_per_us;
    while ((DWT->CYCCNT - start) < delay_cycles) {
        // Wait
    }
} 